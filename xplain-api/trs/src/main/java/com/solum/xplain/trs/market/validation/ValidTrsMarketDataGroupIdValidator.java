package com.solum.xplain.trs.market.validation;

import com.solum.xplain.trs.market.TrsMarketDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;

@AllArgsConstructor
public class ValidTrsMarketDataGroupIdValidator
    implements ConstraintValidator<ValidTrsMarketDataGroupId, String> {

  private final ObjectProvider<TrsMarketDataGroupRepository> provider;

  public boolean isValid(String obj, ConstraintValidatorContext context) {
    var repository = provider.getIfAvailable();
    if (StringUtils.isEmpty(obj) || repository == null) {
      return true;
    } else {
      return repository.dataGroupName(obj).isPresent();
    }
  }
}
