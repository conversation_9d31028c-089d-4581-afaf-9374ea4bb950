package com.solum.xplain.trs.valuation.value;

import com.opengamma.strata.product.common.PayReceive;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class NonMtmAccruedMetricsView {
  private NonMtmLegAccruedMetricsView payLegMetrics;
  private NonMtmLegAccruedMetricsView receiveLegMetrics;

  public NonMtmLegAccruedMetricsView legMetrics(PayReceive payReceive) {
    if (payReceive.isPay()) {
      return payLegMetrics;
    } else {
      return receiveLegMetrics;
    }
  }
}
