package com.solum.xplain.trs.portfolio;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collation = "en")
@FieldNameConstants
public class NonMtmPortfolioItem extends VersionedTrsTrade {

  public static final String NON_MTM_PORTFOLIO_ITEM_COLLECTION = "nonMtmPortfolioItem";

  private String portfolioId;
  private String externalPortfolioId;
  private String externalCompanyId;
  private String externalEntityId;
}
