package com.solum.xplain.workflow;

import com.solum.xplain.workflow.repository.ProcessExecutionRepository;
import com.solum.xplain.workflow.service.command.StateMerger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories(basePackageClasses = {ProcessExecutionRepository.class})
public class WorkflowMongoConfig {
  @Autowired
  void configureStateMerger(MappingMongoConverter mappingMongoConverter) {
    StateMerger.configureMappingMongoConverter(mappingMongoConverter);
  }
}
