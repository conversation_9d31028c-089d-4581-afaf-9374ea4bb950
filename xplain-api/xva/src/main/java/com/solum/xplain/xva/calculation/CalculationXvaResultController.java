package com.solum.xplain.xva.calculation;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_VIEW_XVA_CALCULATION_RESULTS;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xva.calculation.entity.XvaPartyExposureItem;
import com.solum.xplain.xva.calculation.entity.XvaPartyResult;
import com.solum.xplain.xva.calculation.value.XvaCounterparty;
import com.solum.xplain.xva.calculation.value.XvaPartyExposureCharts;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequestMapping("/calculation-result")
public class CalculationXvaResultController {

  private final CalculationResultXvaControllerService service;

  public CalculationXvaResultController(CalculationResultXvaControllerService service) {
    this.service = service;
  }

  @Operation(summary = "Get calculation exposure party results")
  @GetMapping(value = "/{calculationResultId}/xva/party-results")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<ScrollableEntry<XvaPartyResult>> getCalculationXvaPartyResults(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.getCalculationXvaPartyResults(authentication, id, tableFilter, scrollRequest));
  }

  @Operation(summary = "Get calculation party results in csv format")
  @GetMapping(value = "/{calculationResultId}/xva/party-results-csv")
  @CommonErrors
  @SortedFiltered
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<ByteArrayResource> getCalculationXvaPartyResultsCsv(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      @RequestParam LocalDate stateDate,
      Sort sort,
      TableFilter tableFilter,
      Authentication authentication) {
    return eitherErrorItemFileResponse(
        service.exportCalculationXvaPartyResults(authentication, stateDate, id, sort, tableFilter));
  }

  @Operation(summary = "Get calculation exposure counterparties")
  @GetMapping(value = "/{calculationResultId}/xva/party-exposure-counterparties")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<List<XvaCounterparty>> getCalculationXvaPartyExposureCounterparties(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.getCalculationXvaPartyExposureCounterparties(authentication, id));
  }

  @Operation(summary = "Get calculation exposure party exposures")
  @GetMapping(value = "/{calculationResultId}/xva/party-exposures/{counterPartyName}")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<ScrollableEntry<XvaPartyExposureItem>> getCalculationXvaPartyExposures(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      @PathVariable("counterPartyName") String counterPartyName,
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.getCalculationXvaPartyExposures(
            authentication, id, counterPartyName, tableFilter, scrollRequest));
  }

  @Operation(summary = "Get calculation xva party exposure data")
  @GetMapping(value = "/{calculationResultId}/xva/party-exposures/{counterPartyName}/csv")
  @CommonErrors
  @SortedFiltered
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<ByteArrayResource> getCalculationXvaPartyExposuresCsv(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      @PathVariable("counterPartyName") String counterPartyName,
      @RequestParam LocalDate stateDate,
      TableFilter tableFilter,
      Sort sort,
      Authentication authentication) {
    return eitherErrorItemFileResponse(
        service.exportXvaPartyExposures(
            authentication, stateDate, id, counterPartyName, sort, tableFilter));
  }

  @Operation(summary = "Get calculation xva party exposure charts")
  @GetMapping(value = "/{calculationResultId}/xva/party-exposures/{counterPartyName}/charts")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_CALCULATION_RESULTS)
  public ResponseEntity<XvaPartyExposureCharts> getCalculationXvaPartyExposuresCharts(
      @ValidObjectId @PathVariable("calculationResultId") String id,
      @PathVariable("counterPartyName") String counterPartyName,
      Authentication authentication) {
    return eitherErrorItemResponse(
        service.xvaPartyExposureCharts(authentication, id, counterPartyName));
  }
}
