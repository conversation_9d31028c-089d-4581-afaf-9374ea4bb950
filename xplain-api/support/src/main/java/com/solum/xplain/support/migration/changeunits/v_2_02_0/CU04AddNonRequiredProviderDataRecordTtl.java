package com.solum.xplain.support.migration.changeunits.v_2_02_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;

import com.solum.xplain.support.retention.value.RetentionProperties;
import com.solum.xplain.xm.excmngmt.process.data.NonRequiredProviderData;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.index.Index;

@AllArgsConstructor
@ChangeUnit(order = "v02.02.00_04", id = "v2.02.0_04", author = "xplain")
public class CU04AddNonRequiredProviderDataRecordTtl {
  private final MongoOperations mongoOperations;
  private final RetentionProperties retentionProperties;

  private static final String TTL = "TTL";

  @BeforeExecution
  public void beforeExecution() {
    mongoOperations
        .indexOps(NonRequiredProviderData.NON_REQUIRED_PROVIDER_DATA_COLLECTION)
        .ensureIndex(
            new Index()
                .named(indexName(TTL, "modifiedAt"))
                .on("modifiedAt", Direction.ASC)
                .expire(retentionProperties.getWorkflowRetentionDuration()));
  }

  @Execution
  public void execute() {
    // Execute not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
