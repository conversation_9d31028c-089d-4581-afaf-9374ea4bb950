package com.solum.xplain.support.retention.explicit.secmaster;

import static com.solum.xplain.secmaster.entity.SecMasterTradeWriteEntity.SEC_MASTER_TRADE_WRITE_COLLECTION;

import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity;
import com.solum.xplain.secmaster.entity.SecMasterTradeWriteEntity;
import com.solum.xplain.support.retention.daterange.DateRangeValuesRetentionManager;
import com.solum.xplain.support.retention.explicit.ExplicitCollectionRetentionManager;
import com.solum.xplain.support.retention.value.DocumentValidities;
import com.solum.xplain.support.retention.value.RemovalSummary;
import org.springframework.stereotype.Component;

@Component
public class SecMasterRetentionManager implements ExplicitCollectionRetentionManager {

  private final DateRangeValuesRetentionManager<TradeValue, SecMasterTradeWriteEntity>
      retentionManager;

  public SecMasterRetentionManager(SecMasterSupportRepository supportRepository) {
    this.retentionManager =
        new DateRangeValuesRetentionManager<>(
            supportRepository, SEC_MASTER_TRADE_WRITE_COLLECTION, false);
  }

  @Override
  public RemovalSummary clean(DocumentValidities validities) {
    return retentionManager.clean(validities);
  }

  @Override
  public boolean matchesCollection(Class<?> collectionClass) {
    return SecMasterTradeWriteEntity.class.equals(collectionClass);
  }

  @Override
  public boolean matchesCollectionToIgnore(Class<?> collectionClass) {
    return SecMasterTradeReadEntity.class.equals(collectionClass);
  }
}
