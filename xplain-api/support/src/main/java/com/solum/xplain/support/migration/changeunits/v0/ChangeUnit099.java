package com.solum.xplain.support.migration.changeunits.v0;

import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@AllArgsConstructor
@ChangeUnit(order = "099", id = "99", author = "nathan")
public class ChangeUnit099 {
  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    // validFrom_recordDate
    mongoTemplate
        .indexOps(CreditCurve.class)
        .ensureIndex(
            new Index()
                .named("validFrom_recordDate")
                .on("validFrom", Direction.DESC)
                .on("recordDate", Direction.DESC));
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
