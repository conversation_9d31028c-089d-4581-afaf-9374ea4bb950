package com.solum.xplain.calculation.simulation.daterange.validation;

import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ValidSimulationDurationValidator
    implements ConstraintValidator<ValidSimulationDuration, DateRangeSimulationCalculationForm> {
  private static final String MAX_DURATION_TEMPLATE =
      "Simulation duration must be less than or equal to %s days";
  private static final String DATE_ORDER_TEMPLATE =
      "Valuation end date must be after valuation start date";

  private final Integer maxDuration;

  public ValidSimulationDurationValidator(
      @Value("${app.simulation.max-days}") Integer maxDuration) {
    this.maxDuration = maxDuration;
  }

  @Override
  public boolean isValid(
      DateRangeSimulationCalculationForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (value != null
        && value.getValuationStartDate() != null
        && value.getValuationEndDate() != null) {
      var duration =
          ChronoUnit.DAYS.between(value.getValuationStartDate(), value.getValuationEndDate());

      if (duration > maxDuration) {
        context
            .buildConstraintViolationWithTemplate(String.format(MAX_DURATION_TEMPLATE, maxDuration))
            .addConstraintViolation();
        return false;
      }

      if (duration < 0) {
        context.buildConstraintViolationWithTemplate(DATE_ORDER_TEMPLATE).addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
