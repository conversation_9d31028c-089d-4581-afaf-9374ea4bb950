package com.solum.xplain.calculation.pnlexplain.validation;

import com.solum.xplain.calculation.pnlexplain.value.PnlExplainCalculationForm;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class ValidMidPriceRequirementsValidator
    implements ConstraintValidator<ValidMidPriceRequirements, PnlExplainCalculationForm> {

  @Override
  public boolean isValid(PnlExplainCalculationForm form, ConstraintValidatorContext context) {

    if (form != null && form.getPriceRequirements() != null) {

      var priceRequirements = form.getPriceRequirements();

      List<InstrumentPriceType> priceRequirementsList =
          List.of(
              priceRequirements.getCurvesPriceType(),
              priceRequirements.getDscCurvesPriceType(),
              priceRequirements.getFxRatesPriceType(),
              priceRequirements.getVolsPriceType(),
              priceRequirements.getVolsSkewsPriceType());

      // ensure all have MID instrument price type
      return priceRequirementsList.stream()
          .allMatch(priceType -> priceType == InstrumentPriceType.MID_PRICE);
    }

    return true;
  }
}
