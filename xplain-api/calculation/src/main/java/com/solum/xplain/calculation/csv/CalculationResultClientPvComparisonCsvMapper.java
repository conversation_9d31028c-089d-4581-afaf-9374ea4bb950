package com.solum.xplain.calculation.csv;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.COMPARE_CLEAN_PV;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.COMPARE_CLIENT_DIRTY_PV;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.COMPARE_CLIENT_PV;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.COMPARE_PV_CLIENT_PV;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.COMPARE_PV_CLIENT_PV_ABS;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.calculation.value.CalculationItemClientPvComparisonView;
import com.solum.xplain.calculation.value.CalculationPortfolioItemView;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.portfolio.csv.TradeInfoCsvMapper;
import com.solum.xplain.core.portfolio.csv.mapper.PayReceiveCsvMapper;
import java.util.List;

public class CalculationResultClientPvComparisonCsvMapper
    extends CsvMapper<CalculationItemClientPvComparisonView> {

  private static final List<CsvColumn<CalculationItemClientPvComparisonView>> COLUMNS =
      List.of(
          CsvColumn.decimal(
              CalculationPortfolioItemView.Fields.metricsCleanPresentValue,
              COMPARE_CLEAN_PV,
              CalculationItemClientPvComparisonView::getMetricsCleanPresentValue),
          CsvColumn.decimal(
              CalculationPortfolioItemView.Fields.metricsPresentValue,
              COMPARE_CLIENT_DIRTY_PV,
              CalculationItemClientPvComparisonView::getMetricsPresentValue),
          CsvColumn.decimal(
              CalculationPortfolioItemView.Fields.metricsClientMetricsPresentValue,
              COMPARE_CLIENT_PV,
              CalculationItemClientPvComparisonView::getMetricsClientMetricsPresentValue),
          CsvColumn.decimal(
              CalculationItemClientPvComparisonView.Fields.metricsPVDiffClientPV,
              COMPARE_PV_CLIENT_PV,
              CalculationItemClientPvComparisonView::getMetricsPVDiffClientPV),
          CsvColumn.decimal(
              CalculationItemClientPvComparisonView.Fields.absPVDiffClientPV,
              COMPARE_PV_CLIENT_PV_ABS,
              CalculationItemClientPvComparisonView::getAbsPVDiffClientPV));
  private final PayReceiveCsvMapper payReceiveCsvMapper;
  private final TradeInfoCsvMapper tradeInfoCsvMapper;

  public CalculationResultClientPvComparisonCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
    payReceiveCsvMapper = new PayReceiveCsvMapper(selectedColumns);
    tradeInfoCsvMapper = new TradeInfoCsvMapper(selectedColumns);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    builder.addAll(tradeInfoCsvMapper.header());
    builder.addAll(super.header());
    builder.addAll(payReceiveCsvMapper.header());
    return builder.build();
  }

  @Override
  public CsvRow toCsvRow(CalculationItemClientPvComparisonView object) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.addAll(tradeInfoCsvMapper.toCsvFields(object));

    builder.addAll(super.toCsvFields(object));

    builder.addAll(payReceiveCsvMapper.toCsvFields(object));

    return new CsvRow(builder.build());
  }
}
