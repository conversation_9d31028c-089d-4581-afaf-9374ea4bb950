package com.solum.xplain.calculation.discounting;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.CapfloorPortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.CdsPortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.CdxPortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.CdxTranchePortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.SwaptionPortfolioItemDiscountingGroupSpecification;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TradeCalculationRequirements {
  private final Currency currency;
  private final CurrencyPair currencyPair;
  private final String creditCurveName;
  private final RateIndex capletIndex;
  private final RateIndex swaptionIndex;
  private final Set<FloatingRateIndex> requiredIndices;

  public static TradeCalculationRequirements newOf(
      PortfolioItemDiscountingGroupSpecification item) {
    var tradeCurrency = item.tradeCurrency();
    var currencyPair = item.currencyPair();
    var creditCurveName = creditCurveName(item);
    var capletIndex = capletIndex(item);
    var swaptionIndex = swaptionIndex(item);
    var tradeIndices = item.tradeRateIndices();

    return new TradeCalculationRequirements(
        tradeCurrency, currencyPair, creditCurveName, capletIndex, swaptionIndex, tradeIndices);
  }

  private static RateIndex capletIndex(PortfolioItemDiscountingGroupSpecification item) {
    if (item instanceof CapfloorPortfolioItemDiscountingGroupSpecification capfloorTradeSubset) {
      return capfloorTradeSubset.capletIndex();
    }
    return null;
  }

  private static RateIndex swaptionIndex(PortfolioItemDiscountingGroupSpecification item) {
    if (item instanceof SwaptionPortfolioItemDiscountingGroupSpecification swaptionTradeSubset) {
      return swaptionTradeSubset.swaptionIndex();
    }
    return null;
  }

  private static String creditCurveName(PortfolioItemDiscountingGroupSpecification item) {
    if (item instanceof CdsPortfolioItemDiscountingGroupSpecification cdsTradeSubset) {
      return cdsTradeSubset.creditCurveName();
    }
    if (item instanceof CdxPortfolioItemDiscountingGroupSpecification cdxTradeSubset) {
      return cdxTradeSubset.creditCurveName();
    }
    if (item
        instanceof CdxTranchePortfolioItemDiscountingGroupSpecification cdxTrancheTradeSubset) {
      return cdxTrancheTradeSubset.creditCurveName();
    }
    return null;
  }
}
