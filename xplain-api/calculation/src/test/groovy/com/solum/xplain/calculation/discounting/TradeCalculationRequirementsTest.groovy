package com.solum.xplain.calculation.discounting

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.capFloor
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.cdsTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.floatFloatSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.inflationSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.overnightFixedSwap
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.swaptionTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.xccySwap

import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecificationBuilder
import java.time.LocalDate
import spock.lang.Specification

class TradeCalculationRequirementsTest extends Specification {

  def "should resolve requirements for CDS trade"() {
    setup:
    def cds = PortfolioItemDiscountingGroupSpecificationBuilder.cdsDscSpec(cdsTradeDetails())
    def requirements = TradeCalculationRequirements.newOf(cds)
    expect:
    requirements.currency == EUR
    requirements.creditCurveName == "SELF_EUR_SNRFOR_CR14"
    requirements.currencyPair == null
    requirements.capletIndex == null
    requirements.requiredIndices == [] as Set
  }

  def "should resolve requirements for IRS Swap trade"() {
    setup:
    def irs = PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(floatFloatSwap())
    def requirements = TradeCalculationRequirements.newOf(irs)
    expect:
    requirements.currency == EUR
    requirements.creditCurveName == null
    requirements.currencyPair == null
    requirements.capletIndex == null
    requirements.requiredIndices.containsAll(IborIndices.USD_LIBOR_3M)
  }

  def "should resolve requirements for XCCY Swap trade"() {
    setup:
    def xccy = PortfolioItemDiscountingGroupSpecificationBuilder.xccyTradeDscSpec(xccySwap(LocalDate.now()))
    def requirements = TradeCalculationRequirements.newOf(xccy)
    expect:
    requirements.currency == USD
    requirements.creditCurveName == null
    requirements.currencyPair == CurrencyPair.of(EUR, USD)
    requirements.capletIndex == null
    requirements.requiredIndices.containsAll(IborIndices.EUR_EURIBOR_3M, IborIndices.USD_LIBOR_3M)
  }

  def "should resolve requirements for caplet trade"() {
    setup:
    def capfloor = PortfolioItemDiscountingGroupSpecificationBuilder.capFloorTradeDscSpec(capFloor())
    def requirements = TradeCalculationRequirements.newOf(capfloor)
    expect:
    requirements.currency == EUR
    requirements.creditCurveName == null
    requirements.currencyPair == null
    requirements.capletIndex == IborIndices.EUR_EURIBOR_3M
    requirements.requiredIndices == [IborIndices.EUR_EURIBOR_3M] as Set
  }

  def "should resolve requirements for swaption trade"() {
    setup:
    def swaption = PortfolioItemDiscountingGroupSpecificationBuilder.swaptionTradeDscSpec(swaptionTradeDetails())
    def requirements = TradeCalculationRequirements.newOf(swaption)
    expect:
    requirements.currency == EUR
    requirements.creditCurveName == null
    requirements.currencyPair == null
    requirements.swaptionIndex == IborIndices.EUR_EURIBOR_3M
    requirements.requiredIndices == [IborIndices.EUR_EURIBOR_3M] as Set
  }

  def "should resolve requirements for FixedOvernight trade"() {
    setup:
    def irs = PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(overnightFixedSwap(LocalDate.now()))
    def requirements = TradeCalculationRequirements.newOf(irs)
    expect:
    requirements.currency == USD
    requirements.creditCurveName == null
    requirements.currencyPair == null
    requirements.capletIndex == null
    requirements.requiredIndices == [OvernightIndices.USD_FED_FUND] as Set
  }

  def "should resolve requirements for FixedInflation trade"() {
    setup:
    def irs = PortfolioItemDiscountingGroupSpecificationBuilder.irsTradeDscSpec(inflationSwap(LocalDate.now()))
    def requirements = TradeCalculationRequirements.newOf(irs)
    expect:
    requirements.currency == EUR
    requirements.creditCurveName == null
    requirements.currencyPair == null
    requirements.capletIndex == null
    requirements.requiredIndices == [PriceIndices.EU_AI_CPI] as Set
  }
}
