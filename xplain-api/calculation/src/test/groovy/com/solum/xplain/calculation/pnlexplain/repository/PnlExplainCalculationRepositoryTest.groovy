package com.solum.xplain.calculation.pnlexplain.repository

import com.solum.xplain.calculation.CalculationResultStatus
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculation
import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class PnlExplainCalculationRepositoryTest extends IntegrationSpecification {

  static def PNL_EXPLAIN_CALCULATION_ID = "pnlExplainCalculationId"
  static def PNL_EXPLAIN_CALCULATION_ID_2 = "pnlExplainCalculationId2"
  static def VAL_DT = LocalDate.of(2021, 1, 1)
  static def VAL_DT_2 = LocalDate.of(2021, 1, 1)
  static def PORTFOLIO_ID = "portfolioId"
  static def REPORTING_CURRENCY = "USD"

  @Resource
  PnlExplainCalculationRepository repository
  @Resource
  MongoOperations operations

  def cleanup() {
    operations.dropCollection(PnlExplainCalculation)
  }

  def "should get pnl explain calculations for portfolio"() {
    setup:
    def pnlCalc = pnlCalculation(PNL_EXPLAIN_CALCULATION_ID)
    def pnlCalc2 = pnlCalculation(PNL_EXPLAIN_CALCULATION_ID_2)
    operations.insert(pnlCalc)
    operations.insert(pnlCalc2)

    when:
    def result = repository.findAllByPortfolioId(PORTFOLIO_ID)
    def resultPtf2 = repository.findAllByPortfolioId("portfolioId2")

    then:
    result.size() == 2

    result[0].getId() == PNL_EXPLAIN_CALCULATION_ID
    result[0].getFirstValuationDate() == VAL_DT
    result[0].getSecondValuationDate() == VAL_DT_2

    result[1].getId() == PNL_EXPLAIN_CALCULATION_ID_2
    result[1].getFirstValuationDate() == VAL_DT
    result[1].getSecondValuationDate() == VAL_DT_2

    and:
    resultPtf2.size() == 0
  }

  def "should update pnl explain calculation status to saved"() {
    setup:
    def pnlCalc = pnlCalculation()
    def otherPnlCalculation = pnlCalculation(PNL_EXPLAIN_CALCULATION_ID_2)
    operations.insert(pnlCalc)
    operations.insert(otherPnlCalculation)

    def resultBeforeUpdate = repository.getById(PNL_EXPLAIN_CALCULATION_ID)

    when:
    repository.findAndUpdateStatusAsSavedById(PNL_EXPLAIN_CALCULATION_ID)
    def resultAfterUpdate = repository.getById(PNL_EXPLAIN_CALCULATION_ID)
    def otherResult = repository.getById(PNL_EXPLAIN_CALCULATION_ID_2)

    then:
    resultBeforeUpdate.getId() == PNL_EXPLAIN_CALCULATION_ID
    resultAfterUpdate.getId() == PNL_EXPLAIN_CALCULATION_ID

    resultBeforeUpdate.getFirstValuationDate() == VAL_DT
    resultBeforeUpdate.getSecondValuationDate() == VAL_DT_2
    resultAfterUpdate.getFirstValuationDate() == VAL_DT
    resultAfterUpdate.getSecondValuationDate() == VAL_DT_2

    resultBeforeUpdate.getCalculationResultStatus() == CalculationResultStatus.IN_PROGRESS
    resultAfterUpdate.getCalculationResultStatus() == CalculationResultStatus.SAVED

    and:
    otherResult.getCalculationResultStatus() == CalculationResultStatus.IN_PROGRESS
  }

  def pnlCalculation(String id = PNL_EXPLAIN_CALCULATION_ID) {
    return PnlExplainCalculation.builder()
      .id(id)
      .calculationResultStatus(CalculationResultStatus.IN_PROGRESS)
      .firstValuationDate(VAL_DT)
      .secondValuationDate(VAL_DT_2)
      .portfolioId(PORTFOLIO_ID)
      .reportingCurrency(REPORTING_CURRENCY)
      .build()
  }
}
