package com.solum.xplain.xm.excmngmt.process;

import static com.opengamma.strata.basics.date.HolidayCalendars.SAT_SUN;
import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static com.solum.xplain.core.error.Error.OBJECT_ALREADY_EXISTS;
import static com.solum.xplain.core.utils.BusinessDayUtils.businessDays;
import static com.solum.xplain.xm.dashboards.entity.Dashboard.COLLECTION_NAME;
import static com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult.batchPreliminary;
import static com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult.preliminary;
import static com.solum.xplain.xm.excmngmt.rules.value.TestScope.PRELIMINARY;
import static com.solum.xplain.xm.excmngmt.rules.value.TestScope.PRELIMINARY_BATCH;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.market.value.MdkProviderBidAskType;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.instrument.ExceptionManagementDataProvider;
import com.solum.xplain.xm.excmngmt.process.value.BreakTestCalculationsPreliminary;
import com.solum.xplain.xm.excmngmt.process.value.PreliminaryCalculation.PreliminaryCalculationSupplier;
import com.solum.xplain.xm.excmngmt.rules.BreakTestRepository;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ExceptionManagementCalculationService {

  private static final Logger LOG = getLogger(ExceptionManagementCalculationService.class);

  private final ExceptionManagementCalculationRepository repository;
  private final BreakTestRepository breakTestRepository;
  private final MarketDataKeyRepository marketDataKeyRepository;
  private final MarketDataExtractionService marketDataExtractionService;
  private final PreliminaryCalculationSupplier preliminaryCalculationSupplier;
  private final MarketDataDashboardDataService dashboardDataService;

  private final AuditEntryService auditEntryService;

  public Either<ErrorItem, List<EntityId>> performPreliminaryBatch(
      Dashboard dashboard, BitemporalDate stateDate) {
    LOG.debug("Batch preliminary calculation started: {}", dashboard.getId());
    var dataProviders = dashboardDataService.dataProviders(dashboard, stateDate);

    var eithers =
        businessDays(dashboard.getDateRange(), SAT_SUN)
            .map(date -> performPreliminaryBatch(date, dashboard, dataProviders, stateDate))
            .toList();
    LOG.debug("Batch preliminary calculation finished: {}", dashboard.getId());

    logPreliminaryBatch(ImmutableList.copyOf(Eithers.filterLeft(eithers)));

    return Eithers.sequenceLeft(eithers)
        .leftMap(l -> CALCULATION_ERROR.entity("Error calculating preliminary batch " + "results"))
        .map(r -> ImmutableList.copyOf(Eithers.filterRight(eithers)));
  }

  private void logPreliminaryBatch(List<ErrorItem> errors) {
    auditEntryService.newEntryWithLogs(
        AuditEntry.of(COLLECTION_NAME, "Batch preliminary cleaning completed"), errors);
  }

  private Either<ErrorItem, EntityId> performPreliminaryBatch(
      LocalDate curveDate,
      Dashboard dashboard,
      List<ExceptionManagementDataProvider> dataProviders,
      BitemporalDate stateDate) {

    return validSetupForDate(curveDate, dashboard)
        .map(
            s ->
                batchPreliminary(
                    dashboard.getId(),
                    curveDate,
                    s.getMdExceptionManagementSetup(),
                    s.getTrsMdExceptionManagementSetup()))
        .flatMap(xm -> perform(xm, stateDate, dashboard, dataProviders, true));
  }

  public Either<ErrorItem, EntityId> performPreliminary(
      Dashboard dashboard, BitemporalDate stateDate) {
    LOG.debug("Preliminary calculation started: {}", dashboard.getId());
    var curveDate = dashboard.getDateRange().singleDate();
    var dataProviders = dashboardDataService.dataProviders(dashboard, stateDate);
    return validSetupForDate(curveDate, dashboard)
        .map(
            s ->
                preliminary(
                    dashboard.getId(),
                    curveDate,
                    s.getMdExceptionManagementSetup(),
                    s.getTrsMdExceptionManagementSetup()))
        .flatMap(xm -> perform(xm, stateDate, dashboard, dataProviders, false));
  }

  private Either<ErrorItem, EntityId> perform(
      ExceptionManagementResult xm,
      BitemporalDate stateDate,
      Dashboard dashboard,
      List<ExceptionManagementDataProvider> dataProviders,
      boolean batchRun) {
    var results = preliminaryResults(xm.getCurveDate(), stateDate, dataProviders, batchRun);
    repository.insertPreliminary(xm, results);
    return Either.right(EntityId.entityId(dashboard.getId()));
  }

  private Either<ErrorItem, Dashboard> validSetupForDate(LocalDate curveDate, Dashboard dashboard) {
    var existing =
        uniqueExceptionManagement(
            curveDate,
            dashboard.getMdExceptionManagementSetup(),
            dashboard.getTrsMdExceptionManagementSetup());
    return existing
        .<Either<ErrorItem, Dashboard>>map(Either::left)
        .orElseGet(() -> Either.right(dashboard));
  }

  private List<InstrumentResultPreliminary> preliminaryResults(
      LocalDate curveDate,
      BitemporalDate stateDate,
      List<ExceptionManagementDataProvider> dataProviders,
      boolean batchRun) {
    var breakTestCalculations = preliminaryBreakTestCalculations(batchRun, stateDate);
    var mdks = marketDataKeyRepository.marketDataKeysGroupedBidAsks(stateDate);
    return dataProviders.stream()
        .map(
            provider ->
                preliminaryResults(curveDate, breakTestCalculations, stateDate, mdks, provider))
        .flatMap(Collection::stream)
        .toList();
  }

  private List<InstrumentResultPreliminary> preliminaryResults(
      LocalDate curveDate,
      BreakTestCalculationsPreliminary breakTestCalculations,
      BitemporalDate stateDate,
      Map<String, Map<String, MdkProviderBidAskType>> mdks,
      ExceptionManagementDataProvider dataProvider) {
    var marketDataGroupId = dataProvider.marketDataGroupId();

    var calculation =
        preliminaryCalculationSupplier.supply(
            curveDate,
            mdks,
            breakTestCalculations,
            cd ->
                marketDataExtractionService.preliminaryData(
                    marketDataGroupId, cd, stateDate, dataProvider.assetGroups()),
            l ->
                marketDataExtractionService.historicalPreliminaryData(
                    marketDataGroupId, curveDate, stateDate, l, dataProvider.assetGroups()));
    return calculation.perform(dataProvider);
  }

  private BreakTestCalculationsPreliminary preliminaryBreakTestCalculations(
      boolean batchRun, BitemporalDate stateDate) {
    var scope = batchRun ? PRELIMINARY_BATCH : PRELIMINARY;
    var tests = breakTestRepository.getAllWithScope(scope, true, stateDate);
    return BreakTestCalculationsPreliminary.ofBreakTests(tests);
  }

  private Optional<ErrorItem> uniqueExceptionManagement(
      LocalDate date,
      @Nullable MdExceptionManagementSetup mdExceptionManagementSetup,
      @Nullable TrsMdExceptionManagementSetup trsMdSetup) {
    var mdId =
        Optional.ofNullable(mdExceptionManagementSetup)
            .map(MdExceptionManagementSetup::getMarketDataGroup)
            .map(EntityReference::getEntityId)
            .orElse(null);
    var trsMdId =
        Optional.ofNullable(trsMdSetup)
            .map(TrsMdExceptionManagementSetup::getMarketDataGroup)
            .map(EntityReference::getEntityId)
            .orElse(null);

    return repository
        .calculationByDateAndMarketData(date, mdId, trsMdId)
        .map(
            v ->
                String.format(
                    "Exception management run exists for date %s and status %s",
                    date, v.getStatus()))
        .map(OBJECT_ALREADY_EXISTS::entity);
  }
}
