package com.solum.xplain.xm.dashboards.views;

import java.time.LocalDate;

public record PortfolioResultCondensedInfo(
    String portfolioId,
    String externalPortfolioId,
    String externalCompanyId,
    String externalEntityId,
    LocalDate valuationDate) {

  public static PortfolioResultCondensedInfo newOf(
      String portfolioId,
      String externalPortfolioId,
      String externalCompanyId,
      String externalEntityId,
      LocalDate valuationDate) {
    return new PortfolioResultCondensedInfo(
        portfolioId, externalPortfolioId, externalCompanyId, externalEntityId, valuationDate);
  }
}
