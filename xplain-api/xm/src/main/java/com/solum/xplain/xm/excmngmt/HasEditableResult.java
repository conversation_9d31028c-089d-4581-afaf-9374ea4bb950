package com.solum.xplain.xm.excmngmt;

import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;

public interface HasEditableResult {
  EntryResultStatus getStatus();

  boolean isResolved();

  /** This property is returned in the API and used by the UI. */
  default boolean isEditable() {
    if (getStatus() == null) {
      return false;
    }
    return switch (getStatus()) {
      case WAITING_RESOLUTION, REJECTED, WAITING_APPROVAL, HOLD -> true;
      case WAITING_SUBMISSION, RAW -> false;
      case VERIFIED -> isResolved();
    };
  }
}
