package com.solum.xplain.xm.settings;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.settings.GenericVersionedSettingsRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.value.ExceptionManagementSettingsProvider;
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsForm;
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

@Repository
public class ExceptionManagementSettingsRepository
    extends GenericVersionedSettingsRepository<
        ExceptionManagementSettings, ExceptionManagementSettingsView>
    implements ExceptionManagementSettingsProvider {
  private final ExceptionManagementSettingsMapper mapper;

  public ExceptionManagementSettingsRepository(
      MongoOperations mongoOperations, ExceptionManagementSettingsMapper mapper) {
    super(mongoOperations, mapper, ExceptionManagementSettings::empty);
    this.mapper = mapper;
  }

  public ExceptionManagementSettings exceptionManagementSettings(BitemporalDate stateDate) {
    return activeEntityOrEmpty(stateDate);
  }

  public Either<ErrorItem, EntityId> save(
      LocalDate stateDate, ExceptionManagementSettingsForm form) {
    return getExactSettings(stateDate)
        .flatMap(settings -> update(settings, form.versionForm(), s -> mapper.from(form, s)));
  }

  public Either<ErrorItem, EntityId> deleteSettingsVersion(LocalDate stateDate) {
    return getExactSettings(stateDate).flatMap(this::checkFirstVersion).flatMap(this::delete);
  }

  @Override
  public String getCurrencyType(BitemporalDate stateDate) {
    ExceptionManagementSettings settings = exceptionManagementSettings(stateDate);
    return settings.getCurrencyType().toString();
  }

  public IpvValueNavLevel getNavLevelEnum(BitemporalDate stateDate) {
    ExceptionManagementSettings settings = exceptionManagementSettings(stateDate);
    return settings.getNavLevel();
  }

  @Override
  public String getNavLevel(BitemporalDate stateDate) {
    return getNavLevelEnum(stateDate).toString();
  }

  @Override
  public Integer getOnboardingPeriod(BitemporalDate stateDate) {
    ExceptionManagementSettings settings = exceptionManagementSettings(stateDate);
    return settings.getOnboardingPeriod();
  }
}
