package com.solum.xplain.xm.dashboards.resolver;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEqualCollection;

import com.solum.xplain.core.portfolio.PortfolioControllerService;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.xm.dashboards.entity.filter.CompanyEntitiesPortfolioFilter;
import com.solum.xplain.xm.dashboards.entity.filter.EntityPortfoliosFilter;
import com.solum.xplain.xm.dashboards.entity.filter.PortfoliosFilter;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class PortfoliosFilterBuilder {

  private static final String PORTFOLIO_LABEL_TEMPLATE = "%s / %s / %s";
  private static final String NO_FILTER = "All";

  private final PortfolioControllerService portfoliosService;

  public PortfoliosFilterBuilder(PortfolioControllerService portfoliosService) {
    this.portfoliosService = portfoliosService;
  }

  public PortfoliosFilter build(List<String> portfolios) {
    var allPortfolios = portfoliosService.getAllActivePortfolios();
    var companyEntities = buildCompanyEntities(portfolios, allPortfolios);
    var companyLabels = buildCompanyLabels(companyEntities, allPortfolios);
    return PortfoliosFilter.newOf(companyEntities, companyLabels);
  }

  public List<String> buildCompanyLabels(
      List<CompanyEntitiesPortfolioFilter> entities, List<PortfolioCondensedView> portfolios) {
    if (IterableUtils.isEmpty(entities)) {
      return List.of(PortfoliosFilter.ALL_PORTFOLIOS_LABEL);
    } else {
      return entities.stream()
          .map(f -> companyEntitiesPortfoliosLabel(portfolios, f))
          .flatMap(Collection::stream)
          .toList();
    }
  }

  public List<CompanyEntitiesPortfolioFilter> buildCompanyEntities(
      List<String> portfolioIds, List<PortfolioCondensedView> portfolios) {
    var hierarchy =
        portfolios.stream()
            .collect(
                groupingBy(
                    PortfolioCondensedView::getCompanyId,
                    groupingBy(
                        PortfolioCondensedView::getEntityId,
                        mapping(PortfolioCondensedView::getId, toList()))));
    return portfolios.stream()
        .filter(p -> IterableUtils.contains(portfolioIds, p.getId()))
        .collect(groupingBy(PortfolioCondensedView::getCompanyId, LinkedHashMap::new, toList()))
        .entrySet()
        .stream()
        .map(
            ce ->
                ce.getValue().stream()
                    .collect(
                        groupingBy(
                            PortfolioCondensedView::getEntityId,
                            LinkedHashMap::new,
                            mapping(PortfolioCondensedView::getId, toList())))
                    .entrySet()
                    .stream()
                    .map(ep -> entityPortfolios(hierarchy, ce.getKey(), ep))
                    .collect(
                        collectingAndThen(
                            toList(), l -> companyEntities(hierarchy, ce.getKey(), l))))
        .collect(collectingAndThen(toList(), l -> companyEntities(hierarchy, l)));
  }

  private List<CompanyEntitiesPortfolioFilter> companyEntities(
      Map<String, Map<String, List<String>>> hierarchy,
      List<CompanyEntitiesPortfolioFilter> entities) {
    return entities.size() == hierarchy.size()
            && entities.stream().allMatch(ce -> IterableUtils.isEmpty(ce.getEntities()))
        ? List.of()
        : entities;
  }

  private CompanyEntitiesPortfolioFilter companyEntities(
      Map<String, Map<String, List<String>>> hierarchy,
      String companyId,
      List<EntityPortfoliosFilter> entityPortfolios) {
    return companyEntities(companyId, hierarchy.get(companyId), entityPortfolios);
  }

  private CompanyEntitiesPortfolioFilter companyEntities(
      String companyId,
      Map<String, List<String>> allCompanyEntities,
      List<EntityPortfoliosFilter> entityPortfolios) {
    var entities =
        allCompanyEntities.size() == entityPortfolios.size()
                && entityPortfolios.stream()
                    .allMatch(ep -> IterableUtils.isEmpty(ep.getPortfolioIds()))
            ? List.<EntityPortfoliosFilter>of()
            : entityPortfolios;
    return CompanyEntitiesPortfolioFilter.newOf(companyId, entities);
  }

  private EntityPortfoliosFilter entityPortfolios(
      Map<String, Map<String, List<String>>> hierarchy,
      String companyId,
      Map.Entry<String, List<String>> entityPortfolioIds) {
    var entityId = entityPortfolioIds.getKey();
    return entityPortfolios(
        entityId, hierarchy.get(companyId).get(entityId), entityPortfolioIds.getValue());
  }

  private EntityPortfoliosFilter entityPortfolios(
      String entityId, List<String> allEntityPortfolios, List<String> selectedPortfolioIds) {
    var portfolioIds =
        isEqualCollection(selectedPortfolioIds, allEntityPortfolios)
            ? List.<String>of()
            : selectedPortfolioIds;
    return EntityPortfoliosFilter.newOf(entityId, portfolioIds);
  }

  private List<String> companyEntitiesPortfoliosLabel(
      List<PortfolioCondensedView> portfolios, CompanyEntitiesPortfolioFilter filter) {
    var companyPortfolios =
        portfolios.stream()
            .filter(p -> Objects.equals(p.getCompanyId(), filter.getCompanyId()))
            .toList();
    if (IterableUtils.isEmpty(filter.getEntities())) {
      return List.of(companyAllPortfoliosLabel(companyPortfolios));
    } else {
      return entitiesPortfoliosLabel(companyPortfolios, filter.getEntities());
    }
  }

  private String companyAllPortfoliosLabel(List<PortfolioCondensedView> companyPortfolios) {
    return companyPortfolios.stream()
        .findFirst()
        .map(p -> label(p.getExternalCompanyId(), NO_FILTER, NO_FILTER))
        .orElse(StringUtils.EMPTY);
  }

  private List<String> entitiesPortfoliosLabel(
      List<PortfolioCondensedView> portfolios, List<EntityPortfoliosFilter> filter) {
    return filter.stream()
        .map(f -> entityPortfoliosLabel(portfolios, f))
        .flatMap(Collection::stream)
        .toList();
  }

  private List<String> entityPortfoliosLabel(
      List<PortfolioCondensedView> portfolios, EntityPortfoliosFilter filter) {
    var entityPortfolios =
        portfolios.stream()
            .filter(p -> Objects.equals(p.getEntityId(), filter.getEntityId()))
            .toList();
    if (IterableUtils.isEmpty(filter.getPortfolioIds())) {
      return List.of(entityAllPortfoliosLabel(entityPortfolios));
    } else {
      return portfoliosLabel(entityPortfolios, filter.getPortfolioIds());
    }
  }

  private String entityAllPortfoliosLabel(List<PortfolioCondensedView> entityPortfolios) {
    return entityPortfolios.stream()
        .findFirst()
        .map(p -> label(p.getExternalCompanyId(), p.getExternalEntityId(), NO_FILTER))
        .orElse(StringUtils.EMPTY);
  }

  private List<String> portfoliosLabel(
      List<PortfolioCondensedView> portfolios, List<String> portfolioIds) {
    return portfolios.stream()
        .filter(p -> IterableUtils.contains(portfolioIds, p.getId()))
        .map(this::portfolioLabel)
        .toList();
  }

  private String portfolioLabel(PortfolioCondensedView portfolio) {
    return label(
        portfolio.getExternalCompanyId(),
        portfolio.getExternalEntityId(),
        portfolio.getExternalPortfolioId());
  }

  private String label(
      String externalCompanyId, String externalEntityId, String externalPortfolioId) {
    return String.format(
        PORTFOLIO_LABEL_TEMPLATE, externalCompanyId, externalEntityId, externalPortfolioId);
  }
}
