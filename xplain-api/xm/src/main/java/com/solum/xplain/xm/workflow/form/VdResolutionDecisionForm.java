package com.solum.xplain.xm.workflow.form;

import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.processipv.form.ResolutionForm;
import com.solum.xplain.xm.excmngmt.processipv.resolution.data.TradeResultResolutionSubTypeReference;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
public record VdResolutionDecisionForm(
    TradeResultResolutionType resolution,
    @Nullable TradeResultResolutionSubTypeReference resolutionSubType,
    BigDecimal newValue,
    String comment,
    ExceptionManagementEvidence evidence,
    AuditUser user) {
  public VdResolutionDecisionForm(
      ResolutionForm resolution, ExceptionManagementEvidence evidence, AuditUser user) {
    this(
        resolution.resolutionType(),
        resolution.resolutionSubType(),
        resolution.newValue(),
        resolution.comment(),
        evidence,
        user);
  }
}
