package com.solum.xplain.xm.workflow.steps.md.resolution;

import static com.solum.xplain.xm.workflow.steps.md.resolution.Errors.resolvedValueIsEmpty;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

/** Workflow step to switch the base value to the value from the secondary provider. */
@Component("mdSwitchProviderStep")
@RequiredArgsConstructor
public class SwitchProviderStep implements ServiceStepExecutor<MdOverlayState, MdOverlayContext> {
  @Override
  public void runStep(StepStateOps<MdOverlayState, MdOverlayContext> ops) {
    // TODO: SXSD-8832 support tertiary data etc like for valuation data
    ProviderData newProviderData = ops.getContext().getSecondaryProviderData();

    if (newProviderData == null || newProviderData.getValue() == null) {
      ops.throwError(resolvedValueIsEmpty(ops.getContext()));
    }

    AttributedValue valuePendingApproval =
        new AttributedValue(newProviderData.getValue(), null, newProviderData.getProvider());
    ops.setOutcome(
        new MutablePropertyValues()
            .add(MdOverlayState.Fields.valuePendingApproval, valuePendingApproval)
            .add(MdOverlayState.Fields.entryStatus, EntryResultStatus.WAITING_APPROVAL));
  }
}
