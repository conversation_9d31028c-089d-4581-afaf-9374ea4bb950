package com.solum.xplain.xm.excmngmt.rulesipv

import com.solum.xplain.core.portfolio.CoreProductType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = IpvBreakTestOverride, includeSuperProperties = true)
class IpvBreakTestOverrideBuilder {

  IpvBreakTestOverrideBuilder() {
    id("overrideId")
    tradeFilter(new TradeFilter(productTypes: [CoreProductType.IRS], rateCcys: ["EUR"], fxPairs: ["EUR/USD"]))
    threshold([BigDecimal.TEN])
    enabled(true)
  }

  static IpvBreakTestOverride override() {
    new IpvBreakTestOverrideBuilder().build()
  }
}
