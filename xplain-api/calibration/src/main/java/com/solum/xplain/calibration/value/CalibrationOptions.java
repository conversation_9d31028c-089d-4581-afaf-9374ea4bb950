package com.solum.xplain.calibration.value;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import java.time.LocalDate;
import lombok.Data;

@Data
public class CalibrationOptions {

  private final CurveGroup curveGroup;

  private final ValidNodesFilter nodesFilter;

  private final CalculationShifts calculationShifts;

  private final CurveConfigMarketStateKey marketStateKey;

  private final CalculationDiscountingType calibrationCurrency;
  private final CalculationStrippingType strippingType;

  private final LocalDate valuationDate;
  private final Currency triangulationCcy;
}
