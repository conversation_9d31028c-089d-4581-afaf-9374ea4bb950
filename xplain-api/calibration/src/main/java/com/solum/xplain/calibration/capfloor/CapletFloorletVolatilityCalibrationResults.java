package com.solum.xplain.calibration.capfloor;

import com.google.common.collect.ImmutableList;
import java.util.List;
import lombok.Data;

@Data
public class CapletFloorletVolatilityCalibrationResults {
  private final List<CapletFloorletVolatilityCalibrationResult> results;

  public static CapletFloorletVolatilityCalibrationResults of(
      List<CapletFloorletVolatilityCalibrationResult> results) {
    return new CapletFloorletVolatilityCalibrationResults(ImmutableList.copyOf(results));
  }
}
