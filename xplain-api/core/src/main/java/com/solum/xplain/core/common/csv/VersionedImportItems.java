package com.solum.xplain.core.common.csv;

import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface VersionedImportItems<V, K, E extends EmbeddedVersionEntity<V>> {

  Map<K, EntityForUpdate<V, E>> getExistingActiveItemsByKeys();

  Map<K, EntityForUpdate<V, E>> getExistingArchivedItemsByKeys();

  Map<K, V> getImportItemsByKeys();

  Set<K> getNewItemsKeys();

  Set<K> getDuplicateActiveItemsKeys();

  Set<K> getDuplicateArchivedItemsKeys();

  Set<K> getSpareItemsKeys();

  Map<E, V> getNewItems();

  Map<EntityForUpdate<V, E>, V> getActiveDuplicateItems();

  Map<EntityForUpdate<V, E>, V> getArchivedDuplicateItems();

  EntityForUpdate<V, E> getExistingActiveItem(K key);

  List<EntityForUpdate<V, E>> getSpareExistingItems();
}
