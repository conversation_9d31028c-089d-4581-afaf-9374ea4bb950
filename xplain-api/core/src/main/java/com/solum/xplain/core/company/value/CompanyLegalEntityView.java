package com.solum.xplain.core.company.value;

import com.solum.xplain.core.audit.value.AuditLogView;
import com.solum.xplain.core.common.team.WithTeamsView;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CompanyLegalEntityView implements WithTeamsView {
  private String id;
  private String name;
  private String externalId;
  private String description;
  private String creatorId;
  private String creatorName;

  private List<String> teamIds;
  private List<String> teamNames;
  private Boolean allowAllTeams;

  private LocalDateTime createdAt;
  private LocalDateTime modifiedAt;
  private String modifiedBy;
  private boolean archived;
  private List<AuditLogView> auditLogs;
}
