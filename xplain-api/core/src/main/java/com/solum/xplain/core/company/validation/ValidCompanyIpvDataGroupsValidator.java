package com.solum.xplain.core.company.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.company.form.IpvValuationProvidersForm;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.product.ProductType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ValidCompanyIpvDataGroupsValidator
    implements ConstraintValidator<
        ValidCompanyIpvDataGroups, Map<ProductType, IpvValuationProvidersForm>> {
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final RequestPathVariablesSupport support;

  public ValidCompanyIpvDataGroupsValidator(
      IpvDataGroupRepository ipvDataGroupRepository, RequestPathVariablesSupport support) {
    this.ipvDataGroupRepository = ipvDataGroupRepository;
    this.support = support;
  }

  @Override
  public boolean isValid(
      Map<ProductType, IpvValuationProvidersForm> value, ConstraintValidatorContext context) {
    String companyId = support.getPathVariable("companyId");
    if (StringUtils.isNotEmpty(companyId)) {
      return Optional.ofNullable(value).stream()
          .map(Map::entrySet)
          .flatMap(Collection::stream)
          .map(Entry::getValue)
          .map(IpvValuationProvidersForm::getIpvDataGroupId)
          .distinct()
          .allMatch(id -> isValidIpvDataGroup(id, companyId));
    }
    return true;
  }

  private boolean isValidIpvDataGroup(String ipvDataGroupId, String companyId) {
    if (StringUtils.isNotEmpty(ipvDataGroupId)) {
      return ipvDataGroupRepository
          .ipvDataGroupCondensedView(ipvDataGroupId)
          .filter(v -> v.isAllowAllCompanies() || v.getCompanyIds().contains(companyId))
          .isPresent();
    }
    return true;
  }
}
