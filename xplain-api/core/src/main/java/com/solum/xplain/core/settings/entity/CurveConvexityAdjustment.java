package com.solum.xplain.core.settings.entity;

import com.google.common.collect.Maps;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.pricer.model.HullWhiteOneFactorPiecewiseConstantParameters;
import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurveConvexityAdjustment {

  private BigDecimal mean;
  private BigDecimal zeroTenorVol;
  private Map<String, BigDecimal> volatilities;

  protected HullWhiteOneFactorPiecewiseConstantParameters resolveDefinition() {
    double[] volTime = new double[volatilities.size()];
    double[] vol = new double[volatilities.size() + 1];
    vol[0] = zeroTenorVol.doubleValue();

    int i = 0;
    for (var e :
        volatilities.entrySet().stream()
            .collect(Collectors.toMap(e -> Tenor.parse(e.getKey()), Map.Entry::getValue))
            .entrySet()
            .stream()
            .sorted(Map.Entry.comparingByKey())
            .toList()) {
      volTime[i] = e.getKey().getPeriod().toTotalMonths() / 12.0;
      vol[i + 1] = e.getValue().doubleValue();
      i++;
    }

    return HullWhiteOneFactorPiecewiseConstantParameters.of(
        mean.doubleValue(), DoubleArray.copyOf(vol), DoubleArray.copyOf(volTime));
  }

  @EqualsAndHashCode.Include(replaces = "mean")
  private BigDecimal normalisedMean() {
    return mean == null ? null : mean.stripTrailingZeros();
  }

  @EqualsAndHashCode.Include(replaces = "zeroTenorVol")
  private BigDecimal normalisedZeroTenorVol() {
    return zeroTenorVol == null ? null : zeroTenorVol.stripTrailingZeros();
  }

  @EqualsAndHashCode.Include(replaces = "volatilities")
  private Map<String, BigDecimal> normalisedVolatilites() {
    return volatilities == null
        ? null
        : Maps.transformValues(volatilities, BigDecimal::stripTrailingZeros);
  }
}
