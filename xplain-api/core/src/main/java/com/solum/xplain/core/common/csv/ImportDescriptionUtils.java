package com.solum.xplain.core.common.csv;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ImportDescriptionUtils {

  private static final String DETAILED_IMPORT_DESCRIPTION_TEMPLATE =
      "Finished %s import with %d errors, %d "
          + "warnings and %d inserts, %d updates, %d "
          + "removals";

  private static final String DETAILED_IMPORT_DESCRIPTION_TEMPLATE_NO_ERROR =
      "Finished %s import with %d inserts, %d updates, %d removals";
  private static final String IMPORT_DESCRIPTION_TEMPLATE =
      "Finished %s import with %d errors, %d warnings and %d changes";
  private static final String IMPORT_EVALUATION_DESCRIPTION_TEMPLATE =
      "Evaluated %s import with %d errors and %d warnings";

  public static String detailedDescription(
      String objectName, int errorsCount, int warningsCount, ImportOverview overview) {
    return String.format(
        DETAILED_IMPORT_DESCRIPTION_TEMPLATE,
        objectName,
        errorsCount,
        warningsCount,
        overview.getInsertedCount(),
        overview.getUpdatedCount(),
        overview.getArchivedCount());
  }

  public static String detailedDescription(ImportOverview importOverview) {
    return String.format(
        DETAILED_IMPORT_DESCRIPTION_TEMPLATE_NO_ERROR,
        importOverview.getIdentifier(),
        importOverview.getInsertedCount(),
        importOverview.getUpdatedCount(),
        importOverview.getArchivedCount());
  }

  public static String description(
      String objectName, int errorsCount, int warningsCount, int changesCount) {
    return String.format(
        IMPORT_DESCRIPTION_TEMPLATE, objectName, errorsCount, warningsCount, changesCount);
  }

  public static String evaluationDescription(
      String objectName, int errorsCount, int warningsCount) {
    return String.format(
        IMPORT_EVALUATION_DESCRIPTION_TEMPLATE, objectName, errorsCount, warningsCount);
  }
}
