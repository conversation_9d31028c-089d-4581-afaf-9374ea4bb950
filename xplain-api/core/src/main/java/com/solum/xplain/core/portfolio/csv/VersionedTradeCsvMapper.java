package com.solum.xplain.core.portfolio.csv;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ALLOCATION_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CLIENT_METRICS_PV;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_COUNTERPARTY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_COUNTERPARTY_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CSA_DISCOUNTING;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_DESCRIPTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_ACCOUNTING_COST;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_DEAL_COST;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_MARKET_CHECK;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_VENDOR_CHECK;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_VENDOR_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ONBOARDING_XPLAIN_CHECK;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REFERENCE_TRADE_ID;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_SWAPTION_SETTLEMENT_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_TRADE_ID;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_TYPE;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.product.csv.ProductCsvMappers;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class VersionedTradeCsvMapper {

  private final ProductCsvMappers mappers;

  public List<CsvField> toFields(VersionedTradeEntity item) {
    var info = item.getTradeDetails().getInfo();
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_TRADE_ID, item.getExternalTradeId()));
    builder.add(new CsvField(TRADE_TYPE, item.getProductType().name()));
    builder.add(new CsvField(TRADE_SWAPTION_SETTLEMENT_DATE, info.getSettlementDate()));
    builder.add(new CsvField(TRADE_CURRENCY, info.getTradeCurrency()));
    if (item.getOnboardingDetails() != null) {
      var onboardingDetails = item.getOnboardingDetails();
      builder.add(new CsvField(TRADE_ONBOARDING_DEAL_COST, onboardingDetails.getDealCost()));
      builder.add(
          new CsvField(TRADE_ONBOARDING_ACCOUNTING_COST, onboardingDetails.getAccountingCost()));
      builder.add(
          new CsvField(
              TRADE_ONBOARDING_XPLAIN_CHECK, isTrue(onboardingDetails.getXplainCostCheck())));
      builder.add(
          new CsvField(
              TRADE_ONBOARDING_MARKET_CHECK, isTrue(onboardingDetails.getMarketConfCheck())));
      builder.add(
          new CsvField(TRADE_ONBOARDING_VENDOR_CHECK, isTrue(onboardingDetails.getVendorCheck())));
      builder.add(
          new CsvField(TRADE_ONBOARDING_VENDOR_DATE, onboardingDetails.getVendorOnboardingDate()));
    }
    if (item.getAllocationTradeDetails() != null) {
      var allocDetails = item.getAllocationTradeDetails();
      builder.add(new CsvField(TRADE_REFERENCE_TRADE_ID, allocDetails.getReferenceTradeId()));
      builder.add(new CsvField(TRADE_ALLOCATION_NOTIONAL, allocDetails.getAllocationNotional()));
      builder.add(
          new CsvField(
              TRADE_COUNTERPARTY,
              allocDetails.getCounterParty() != null
                  ? allocDetails.getCounterParty().toUpperCase()
                  : null));
      builder.add(new CsvField(TRADE_COUNTERPARTY_TYPE, allocDetails.getCounterPartyType()));
      builder.add(new CsvField(TRADE_CSA_DISCOUNTING, allocDetails.getCsaDiscountingGroup()));
      builder.add(new CsvField(TRADE_DATE, allocDetails.getTradeDate()));
      var clientMetrics = allocDetails.getClientMetrics();
      if (clientMetrics != null && clientMetrics.getPresentValue() != null) {
        builder.add(new CsvField(TRADE_CLIENT_METRICS_PV, clientMetrics.getPresentValue()));
      }
      builder.add(new CsvField(TRADE_DESCRIPTION, allocDetails.getDescription()));
    } else {
      builder.add(new CsvField(TRADE_DESCRIPTION, item.getDescription()));
      builder.add(new CsvField(TRADE_DATE, info.getTradeDate()));
      builder.add(
          new CsvField(
              TRADE_COUNTERPARTY,
              info.getCounterParty() != null ? info.getCounterParty().toUpperCase() : null));
      builder.add(new CsvField(TRADE_COUNTERPARTY_TYPE, info.getCounterPartyType()));
      builder.add(new CsvField(TRADE_CSA_DISCOUNTING, info.getCsaDiscountingGroup()));
      var clientMetrics = item.getClientMetrics();
      if (clientMetrics != null && clientMetrics.getPresentValue() != null) {
        builder.add(new CsvField(TRADE_CLIENT_METRICS_PV, clientMetrics.getPresentValue()));
      }
    }
    var mapper = mappers.mapper(item.getProductType());
    builder.addAll(mapper.toCsvFields(item.getTradeDetails()));

    return builder.build();
  }
}
