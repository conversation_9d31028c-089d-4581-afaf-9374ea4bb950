package com.solum.xplain.core.curvegroup.curvecredit.csv.curve;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_QUOTE_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_SENIORITY_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DOC_CLAUSE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.PermissibleConventions.CREDIT_CURRENCIES;
import static com.solum.xplain.core.common.creditindex.CreditIndexCsvUtils.defaultIndexValue;
import static com.solum.xplain.core.common.creditindex.ValidCreditIndexTrancheValidator.validTranche;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDate;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDouble;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.toEnum;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateNumberValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValueInRange;
import static io.atlassian.fugue.Either.right;

import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import com.solum.xplain.core.classifiers.CdsIndex;
import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.classifiers.CreditTranches;
import com.solum.xplain.core.common.creditindex.CreditIndexCsvUtils;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCsvForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class CreditCurveCsvLoader extends GenericCsvLoader<CreditCurveCsvForm, String> {

  static final String CURVE_TYPE_FIELD = "Curve Type";
  static final String REFERENCE_FIELD = "Reference";
  static final String CCY_FIELD = "Ccy";
  static final String SECTOR_FIELD = "Sector";
  static final String DOC_CLAUSE_FIELD = "DocClause";
  static final String SENIORITY_FIELD = "Seniority";
  static final String ENTITY_NAME_FIELD = "Long Name";
  static final String CORP_TICKER_FIELD = "Corp Ticker";
  static final String RECOVERY_RATE_FIELD = "Recovery Rate";
  static final String FIXED_COUPON_FIELD = "Fixed Coupon";
  static final String QUOTE_CONVENTION_FIELD = "Quote Convention";
  static final String CREDIT_INDEX_SERIES_FIELD = "Credit Index Series";
  static final String CREDIT_INDEX_VERSION_FIELD = "Credit Index Version";
  static final String CREDIT_INDEX_FACTOR_FIELD = "Credit Index Factor";
  static final String CREDIT_INDEX_START_DATE_FIELD = "Credit Index Start Date";

  static final String CREDIT_INDEX_TRANCHE_FIELD = "Credit Index Tranche";

  private static final ClassifierSupplier DOC_CLAUSE_SUPPLIER =
      new ClassifierSupplier(DOC_CLAUSE_CLASSIFIER_NAME);
  private static final ClassifierSupplier SECTOR_SUPPLIER =
      new ClassifierSupplier(SECTOR_CLASSIFIER_NAME);
  private static final ClassifierSupplier QUOTE_CONVENTION_SUPPLIER =
      new ClassifierSupplier(CREDIT_QUOTE_CONVENTION_CLASSIFIER);
  private static final ClassifierSupplier SENIORITY_SUPPLIER =
      new ClassifierSupplier(CREDIT_SENIORITY_CLASSIFIER);

  @Override
  protected CsvParserResultBuilder<CreditCurveCsvForm, String> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CreditCurveCsvForm::creditCurveName, Function.identity(), parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    // Validate only common fields headers
    return List.of(
        CURVE_TYPE_FIELD,
        REFERENCE_FIELD,
        ENTITY_NAME_FIELD,
        CCY_FIELD,
        SECTOR_FIELD,
        DOC_CLAUSE_FIELD,
        RECOVERY_RATE_FIELD,
        FIXED_COUPON_FIELD,
        QUOTE_CONVENTION_FIELD);
  }

  @Override
  protected Either<ErrorItem, CreditCurveCsvForm> parseLine(@NonNull CsvRow row) {
    return Steps.begin(parseType(row))
        .then(type -> parseCommon(type, row))
        .yield((type, form) -> parseByType(form, row))
        .flatMap(Function.identity());
  }

  private Either<ErrorItem, CreditCurveType> parseType(CsvRow row) {
    return getFieldValue(row, CURVE_TYPE_FIELD, typeRaw -> toEnum(typeRaw, CreditCurveType.class));
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseByType(CreditCurveCsvForm form, CsvRow row) {
    return switch (form.getCurveType()) {
      case CDS -> parseCds(form, row);
      case CREDIT_INDEX -> parseCreditIndex(form, row);
      case CREDIT_INDEX_TRANCHE -> parseCreditIndexTranche(form, row);
    };
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseCds(
      @NonNull CreditCurveCsvForm form, @NonNull CsvRow row) {
    try {
      var sector = row.getValue(SECTOR_FIELD);
      var docClause = row.getValue(DOC_CLAUSE_FIELD);
      var seniorityStr = row.getValue(SENIORITY_FIELD);

      row.findValue(CORP_TICKER_FIELD).map(String::toUpperCase).ifPresent(form::setCorpTicker);
      row.findValue(ENTITY_NAME_FIELD).ifPresent(form::setEntityLongName);
      form.setSeniority(validateValue(seniorityStr, SENIORITY_SUPPLIER));
      form.setDocClause(validateValue(docClause, DOC_CLAUSE_SUPPLIER));
      form.setSector(validateValue(sector, SECTOR_SUPPLIER));

      return Either.right(form);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseCreditIndex(
      @NonNull CreditCurveCsvForm form, @NonNull CsvRow row) {
    try {
      var startDate = parseDate(row.getValue(CREDIT_INDEX_START_DATE_FIELD));
      var index = CreditIndexCsvUtils.parseCreditIndex(row, ENTITY_NAME_FIELD);
      var sector =
          row.findValue(SECTOR_FIELD)
              .map(s -> validateValue(s, SECTOR_FIELD, SECTOR_SUPPLIER))
              .orElseGet(
                  () ->
                      defaultIndexValue(index, CdsIndex::getSector, CreditSector.UNDEFINED).name());
      var docClause =
          row.findValue(DOC_CLAUSE_FIELD)
              .map(str -> validateValue(str, DOC_CLAUSE_FIELD, DOC_CLAUSE_SUPPLIER))
              .orElseGet(
                  () ->
                      defaultIndexValue(index, CdsIndex::getDocClause, CreditDocClause.UNDEFINED)
                          .name());
      var factor =
          row.findValue(CREDIT_INDEX_FACTOR_FIELD)
              .map(CsvLoaderUtils::parseDouble)
              .orElse(BigDecimal.ONE.doubleValue());

      form.setCreditIndexFactor(BigDecimal.valueOf(validateValueInRange(factor, 0, 1)));
      form.setCreditIndexStartDate(startDate);
      form.setEntityLongName(index);
      form.setSector(sector);
      form.setDocClause(docClause);

      row.findValue(CREDIT_INDEX_SERIES_FIELD)
          .map(Integer::parseInt)
          .ifPresent(form::setCreditIndexSeries);
      row.findValue(CREDIT_INDEX_VERSION_FIELD)
          .map(Integer::parseInt)
          .ifPresent(form::setCreditIndexVersion);

      return right(form);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseCreditIndexTranche(
      CreditCurveCsvForm form, @NonNull CsvRow row) {
    return parseCreditIndex(form, row).flatMap(f -> parseIndexTrancheFields(f, row));
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseIndexTrancheFields(
      CreditCurveCsvForm form, @NonNull CsvRow row) {
    try {
      var tranche = CreditTranches.parseFromLabel(row.getValue(CREDIT_INDEX_TRANCHE_FIELD));
      var validTranche =
          validTranche(tranche, form.getEntityLongName())
              .leftMap(ErrorItem::getDescription)
              .fold(
                  error -> validateValue(tranche, CREDIT_INDEX_TRANCHE_FIELD, error, false),
                  t -> t);
      form.setCreditIndexTranche(validTranche);
      return right(form);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  private Either<ErrorItem, CreditCurveCsvForm> parseCommon(
      @NonNull CreditCurveType type, @NonNull CsvRow row) {
    try {
      var form = new CreditCurveCsvForm(type);
      var reference = parseIdentifier(row, REFERENCE_FIELD);
      var ccyStr = row.getValue(CCY_FIELD);
      String cdsQuoteConventionStr = row.getValue(QUOTE_CONVENTION_FIELD);

      form.setReference(reference);
      form.setCurrency(validateValue(ccyStr, () -> CREDIT_CURRENCIES));

      double recoveryRate = 0.4; // default
      if (row.headers().contains(RECOVERY_RATE_FIELD)) {
        var value = row.findValue(RECOVERY_RATE_FIELD).map(String::trim);
        if (value.isPresent() && !value.get().isEmpty()) {
          recoveryRate = validateValueInRange(parseDouble(row, RECOVERY_RATE_FIELD), 0, 1);
        }
      }
      form.setRecoveryRate(BigDecimal.valueOf(recoveryRate));

      form.setQuoteConvention(validateValue(cdsQuoteConventionStr, QUOTE_CONVENTION_SUPPLIER));

      if (!CdsQuoteConvention.PAR_SPREAD.name().equals(cdsQuoteConventionStr)) {
        var fixedRate = parseDouble(row, FIXED_COUPON_FIELD);
        form.setFixedCoupon(
            validateNumberValue(
                fixedRate, FIXED_COUPON_FIELD, Constants.CREDIT_CURVE_FIXED_RATE_BPS));
      }

      return right(form);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }
}
