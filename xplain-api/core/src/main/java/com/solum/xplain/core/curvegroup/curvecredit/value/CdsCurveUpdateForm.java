package com.solum.xplain.core.curvegroup.curvecredit.value;

import com.solum.xplain.core.common.validation.UniqueValues;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType;
import com.solum.xplain.core.curvegroup.curvecredit.validation.FundingNodesAllowed;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditNodes;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidFundingNodes;
import jakarta.validation.Valid;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ValidFundingNodes
@FundingNodesAllowed
public class CdsCurveUpdateForm extends CreditCurveCommonForm {

  private String entityLongName;

  private String corpTicker;

  @Valid
  @UniqueValues
  @ValidCreditNodes(type = CreditCurveNodeType.CDS)
  private List<CreditCurveNodeForm> cdsNodes;

  @Valid
  @UniqueValues
  @ValidCreditNodes(type = CreditCurveNodeType.FUNDING)
  private List<CreditCurveNodeForm> fundingNodes;
}
