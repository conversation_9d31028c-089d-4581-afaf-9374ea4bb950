package com.solum.xplain.core.authentication.converter

import com.solum.xplain.core.authentication.CacheableAuthenticationProxy
import com.solum.xplain.core.authentication.XplainPrincipalResolver
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.config.properties.OAuth2CustomProperties
import com.solum.xplain.core.config.properties.OAuth2CustomProperties.ClaimsProperties
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal
import org.springframework.security.oauth2.server.resource.introspection.SpringOpaqueTokenIntrospector
import spock.lang.Specification

class OpaqueTokenToPrincipalIntrospectorTest extends Specification {

  def springOpaqueTokenIntrospector = Mock(SpringOpaqueTokenIntrospector)
  def resolver = Mock(XplainPrincipalResolver)
  def proxy = Mock(CacheableAuthenticationProxy)
  def props = new OAuth2CustomProperties(claims: new ClaimsProperties(
  usernameClaim: "UC",
  teamsClaim: "TC",
  rolesClaim: "RC",
  principalTypeClaim: "GT",
  clientIdClaim: "AP",
  principalTypeClientValue: "client-credentials",
  ))
  def converter = new OpaqueTokenToPrincipalIntrospector(springOpaqueTokenIntrospector, resolver, proxy, props)

  def "should convert to principal"() {
    setup:
    def principal = new XplainPrincipal("id", "name", [], ["A": "B"], [], [], [])
    1 * proxy.fetchCachedOrNewPrincipal("token", _) >> principal

    when:
    def result = converter.introspect("token")

    then:
    result.getName() == "name"
  }

  def "should resolve user principal"() {
    setup:
    def attributes = ["UC": "name", "TC": ["team"], "RC" : ["role"]]
    def authorities = []
    1 * springOpaqueTokenIntrospector.introspect("token") >> new DefaultOAuth2AuthenticatedPrincipal(
      "id",
      attributes,
      authorities
      )

    def principal = new XplainPrincipal("id", "name", [], attributes, [], [], [])
    1 * resolver.resolvePrincipal("id", "name", null, ["role"], ["team"], authorities, attributes, false) >> principal

    when:
    def result = converter.resolvePrincipal("token")

    then:
    result == principal
  }

  def "should resolve user principal with client specified"() {
    setup:
    def attributes = ["UC": "name", "TC": ["team"], "RC" : ["role"], "AP": "client"]
    def authorities = []
    1 * springOpaqueTokenIntrospector.introspect("token") >> new DefaultOAuth2AuthenticatedPrincipal(
      "id",
      attributes,
      authorities
      )

    def principal = new XplainPrincipal("id", "name", [], attributes, [], [], [])
    1 * resolver.resolvePrincipal("id", "name", "client", ["role"], ["team"], authorities, attributes, false) >> principal

    when:
    def result = converter.resolvePrincipal("token")

    then:
    result == principal
  }

  def "should resolve user principal with client and principal type specified"() {
    setup:
    def attributes = ["UC": "name", "TC": ["team"], "RC" : ["role"], "AP": "client", "GT": "user"]
    def authorities = []
    1 * springOpaqueTokenIntrospector.introspect("token") >> new DefaultOAuth2AuthenticatedPrincipal(
      "id",
      attributes,
      authorities
      )

    def principal = new XplainPrincipal("id", "name", [], attributes, [], [], [])
    1 * resolver.resolvePrincipal("id", "name", "client", ["role"], ["team"], authorities, attributes, false) >> principal

    when:
    def result = converter.resolvePrincipal("token")

    then:
    result == principal
  }

  def "should resolve client principal"() {
    setup:
    def attributes = ["UC": "name", "GT": "client-credentials", "AP": "client"]
    def authorities = []
    1 * springOpaqueTokenIntrospector.introspect("token") >> new DefaultOAuth2AuthenticatedPrincipal(
      "id",
      attributes,
      authorities
      )

    def principal = new XplainPrincipal("id", "name", [], attributes, [], [], [])
    1 * resolver.resolvePrincipal("id", "name", "client", null, null, authorities, attributes, true) >> principal

    when:
    def result = converter.resolvePrincipal("token")

    then:
    result == principal
  }
}
