package com.solum.xplain.core.ipv.tradeleveloverride.csv

import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE

import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedCompanyView
import com.solum.xplain.core.providers.DataProviderRepository
import com.solum.xplain.core.providers.enums.DataProviderType
import com.solum.xplain.core.providers.value.DataProviderView
import com.solum.xplain.core.users.UserBuilder
import spock.lang.Specification

class TradeLevelOverrideCsvLoaderFactoryTest extends Specification {

  def ipvDataGroupRepository = Mock(IpvDataGroupRepository)
  def dataProviderRepository = Mock(DataProviderRepository)
  def tradeLevelOverrideCsvLoaderFactory = new TradeLevelOverrideCsvLoaderFactory(ipvDataGroupRepository, dataProviderRepository)

  def "should create DealExceptionCsvLoader"() {
    setup:
    def user = UserBuilder.user()

    def ipvDataGroup = new IpvDataGroupCondensedCompanyView()
    ipvDataGroup.name = "IPV_GROUP"

    def dataProviderView = new DataProviderView()
    dataProviderView.externalId = "PROVIDER_1"

    def xplainProviderView = new DataProviderView()
    xplainProviderView.externalId = XPLAIN_PROVIDER_CODE

    def navProviderView = new DataProviderView()
    navProviderView.externalId = NAV_PROVIDER_CODE

    1 * ipvDataGroupRepository.activeUserIpvDataGroups(EntityTeamFilter.filter(user)) >> [ipvDataGroup]

    1 * dataProviderRepository.dataProvidersViewListForType(DataProviderType.VALUATION) >> [dataProviderView, xplainProviderView, navProviderView]

    when:
    def loader = tradeLevelOverrideCsvLoaderFactory.loader(user)

    then:
    loader instanceof TradeLevelOverrideCsvLoader

    loader.existingIpvDataGroupNames.size() == 1
    loader.existingIpvDataGroupNames[0] == "IPV_GROUP"

    loader.existingValuationDataProviders.size() == 2
    loader.existingValuationDataProviders[0] == "PROVIDER_1"
    loader.existingValuationDataProviders[1] == "XPLAIN"
  }
}
