package com.solum.xplain.core.curvegroup.volatility.entity

import static com.solum.xplain.core.market.MarketDataSample.VAL_DT

import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import com.solum.xplain.core.market.MarketDataSample
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = VolatilitySurface, includeSuperProperties = true)
class VolatilitySurfaceBuilder {

  VolatilitySurfaceBuilder() {
    curveGroupId("curveGroupId")
    entityId(new ObjectId().toString())
    comment("comment")
    validFrom(NewVersionFormV2.ROOT_DATE)
    recordDate(MarketDataSample.STATE_DATE.recordDate.minusSeconds(1))
    state(State.ACTIVE)
    name("EUR 3M Vols")
    XInterpolator(CurveInterpolators.LINEAR.name)
    XExtrapolatorLeft(CurveExtrapolators.FLAT.name)
    XExtrapolatorRight(CurveExtrapolators.FLAT.name)
    YInterpolator(CurveInterpolators.LINEAR.name)
    YExtrapolatorLeft(CurveExtrapolators.FLAT.name)
    YExtrapolatorRight(CurveExtrapolators.FLAT.name)
    skewType(VolatilitySurfaceType.STRIKE)
    capletValuationModel(CapletValuationModel.BLACK)
    sabr(false)
    nodes([])
    skewNodes([])
    capletVolatilities([])
  }

  static surface() {
    new VolatilitySurfaceBuilder().build()
  }

  static surfaceWith(Closure<VolatilitySurfaceBuilder> closure) {
    new VolatilitySurfaceBuilder()
      .with(closure)
      .build()
  }

  static surfaceWithNodes(String name = "EUR 3M Vols") {
    sampleSurface(VolatilitySurfaceType.MONEYNESS, name)
  }

  static surfaceWithOnlyAtmNodes() {
    def surface = sampleSurface(VolatilitySurfaceType.ATM_ONLY)
    surface.setSkewNodes(null)
    surface
  }

  def static sampleSurface(VolatilitySurfaceType skewType, String name = "EUR 3M Vols") {
    def b = new VolatilitySurfaceBuilder()
      .name(name)
      .validFrom(VAL_DT)
      .skewType(skewType)
      .state(State.ACTIVE)
      .nodes([
        volSurfaceNode("1Y", "1Y"),
        volSurfaceNode("2Y", "1Y"),
        volSurfaceNode("1Y", "2Y"),
        volSurfaceNode("2Y", "2Y")
      ])
      .capletVolatilities([
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.67).build()
      ])

    if (skewType == VolatilitySurfaceType.STRIKE) {
      b.skewNodes([volSurfacSkew(0.01), volSurfacSkew(0.1)])
    }

    if (skewType == VolatilitySurfaceType.MONEYNESS) {
      b.skewNodes([volSurfacSkew(0.0001), volSurfacSkew(0.001)])
    }

    b.build()
  }

  def static sampleSurfaceInvalidSurfaceNodesMatrix() {
    new VolatilitySurfaceBuilder()
      .validFrom(VAL_DT)
      .skewType(VolatilitySurfaceType.STRIKE)
      .nodes([
        volSurfaceNode("3Y", "1Y"),
        volSurfaceNode("5Y", "2Y"),
        volSurfaceNode("6Y", "1Y")
      ])
      .skewNodes([volSurfacSkew(0.01), volSurfacSkew(0.1)])
      .capletVolatilities([
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.67).build()
      ])
      .build()
  }

  def static volSurfaceNode(String tenor, String expiry) {
    new VolatilitySurfaceNode(tenor: tenor, expiry: expiry)
  }

  def static volSurfacSkew(BigDecimal strike) {
    new VolatilitySurfaceSkew(surfaceSkewId: ObjectId.get().toString(),
    skewValue: strike)
  }

  static surfaceWithOvernightIndex(String name, OvernightIndex overnightIndex) {
    return new VolatilitySurfaceBuilder()
      .name(name)
      .validFrom(VAL_DT)
      .skewType(VolatilitySurfaceType.MONEYNESS)
      .state(State.ACTIVE)
      .nodes([
        volSurfaceNode("1Y", "1Y"),
        volSurfaceNode("2Y", "1Y"),
        volSurfaceNode("1Y", "2Y"),
        volSurfaceNode("2Y", "2Y")
      ])
      .capletVolatilities([
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.57).build(),
        new CapletVolatilityNodeBuilder().tenor("1Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("2Y").strike(0.67).build(),
        new CapletVolatilityNodeBuilder().tenor("3Y").strike(0.67).build()
      ])
      .skewNodes([volSurfacSkew(0.0001), volSurfacSkew(0.001)])
      .build()
  }
}
