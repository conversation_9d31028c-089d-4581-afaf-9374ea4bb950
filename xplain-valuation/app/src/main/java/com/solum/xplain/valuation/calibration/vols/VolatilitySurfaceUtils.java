package com.solum.xplain.valuation.calibration.vols;

import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.opengamma.strata.market.surface.interpolator.GridSurfaceInterpolator;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceConfig;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class VolatilitySurfaceUtils {

  public static GridSurfaceInterpolator gridSurfaceInterpolator(SurfaceConfig config) {
    return GridSurfaceInterpolator.of(
        CurveInterpolator.of(config.getXInterpolator()),
        CurveExtrapolator.of(config.getXExtrapolatorLeft()),
        CurveExtrapolator.of(config.getXExtrapolatorRight()),
        CurveInterpolator.of(config.getYInterpolator()),
        CurveExtrapolator.of(config.getYExtrapolatorLeft()),
        CurveExtrapolator.of(config.getYExtrapolatorRight()));
  }
}
