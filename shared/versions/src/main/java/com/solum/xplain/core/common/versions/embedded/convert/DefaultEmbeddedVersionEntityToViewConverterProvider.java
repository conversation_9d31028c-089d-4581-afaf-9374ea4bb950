package com.solum.xplain.core.common.versions.embedded.convert;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import java.util.List;
import java.util.function.BiFunction;

public class DefaultEmbeddedVersionEntityToViewConverterProvider<
        V, E extends EmbeddedVersionEntity<V>, R extends DateRangeVersionedEntity>
    implements EmbeddedVersionEntityToViewConverterProvider<V, E, R> {

  private final DefaultEmbeddedVersionEntityToViewConverter<V, E, R> defaultConverter;

  public DefaultEmbeddedVersionEntityToViewConverterProvider(BiFunction<E, V, R> converter) {
    this.defaultConverter = new DefaultEmbeddedVersionEntityToViewConverter<>(converter);
  }

  @Override
  public EmbeddedVersionEntityToViewConverter<V, E, R> provideForValues(
      List<V> values, List<E> entities) {
    return defaultConverter;
  }
}
