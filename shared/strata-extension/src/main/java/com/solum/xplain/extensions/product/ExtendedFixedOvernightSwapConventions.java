package com.solum.xplain.extensions.product;

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;
import static com.opengamma.strata.basics.date.DayCounts.ACT_360;
import static com.opengamma.strata.basics.date.DayCounts.ACT_365F;
import static com.opengamma.strata.basics.date.DayCounts.ACT_ACT_ISDA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.BRBD;
import static com.opengamma.strata.basics.index.OvernightIndices.AUD_AONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.CAD_CORRA;
import static com.opengamma.strata.basics.index.OvernightIndices.CHF_SARON;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_ESTR;
import static com.opengamma.strata.basics.index.OvernightIndices.GBP_SONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.JPY_TONAR;
import static com.opengamma.strata.basics.index.OvernightIndices.NOK_NOWA;
import static com.opengamma.strata.basics.index.OvernightIndices.NZD_NZIONA;
import static com.opengamma.strata.basics.index.OvernightIndices.THB_THOR;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_FED_FUND;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR;
import static com.opengamma.strata.basics.schedule.Frequency.P12M;
import static com.opengamma.strata.basics.schedule.Frequency.P3M;
import static com.opengamma.strata.basics.schedule.Frequency.P6M;
import static com.opengamma.strata.basics.schedule.Frequency.TERM;
import static com.opengamma.strata.product.swap.FixedAccrualMethod.DEFAULT;
import static com.opengamma.strata.product.swap.OvernightAccrualMethod.COMPOUNDED;
import static com.opengamma.strata.product.swap.OvernightAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.CLP_TNA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.COP_OIBR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.CZK_CZEONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.DKK_DESTR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.HKD_HONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.HUF_BUBORON;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.IDR_INDONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.ILS_SHIR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.INR_OMIBOR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.KRW_KOFR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MYR_MYOR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.PLN_POLONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.PLN_WIRON;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.RUB_RUONIA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.SEK_SWESTR;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.SGD_SORA;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.TRY_TLREF;
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.ZAR_ZARONIA;
import static com.solum.xplain.extensions.index.OffshoreIndices.CLP_TNA_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.COP_OIBR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.ILS_SHIR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.INR_OMIBOR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.KRW_KOFR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.MYR_MYOR_OFFSHORE;
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THOR_OFFSHORE;

import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.OvernightIndices;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.ImmutableFixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.solum.xplain.extensions.index.OffshoreIndices;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@SuppressWarnings("unused")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExtendedFixedOvernightSwapConventions {

  public static final FixedOvernightSwapConvention AUD_FIXED_TERM_AONIA_OIS =
      makeConvention(
          "AUD-FIXED-TERM-AONIA-OIS", AUD_AONIA, ACT_365F, TERM, 2, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention AUD_FIXED_1Y_AONIA_OIS =
      makeConvention("AUD-FIXED-1Y-AONIA-OIS", AUD_AONIA, ACT_365F, P12M, 2, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention AUD_FIXED_SHORTTERM_AONIA_OIS =
      makeConvention("AUD-FIXED-SHORTTERM-AONIA-OIS", AUD_AONIA, ACT_365F, TERM, 2, 1, FOLLOWING);

  public static final FixedOvernightSwapConvention CAD_FIXED_TERM_CORRA_OIS =
      makeConvention(
          "CAD-FIXED-TERM-CORRA-OIS", CAD_CORRA, ACT_365F, TERM, 1, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CAD_FIXED_6M_CORRA_OIS =
      makeConvention("CAD-FIXED-6M-CORRA-OIS", CAD_CORRA, ACT_365F, P6M, 1, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CAD_FIXED_SHORTTERM_CORRA_OIS =
      makeConvention("CAD-FIXED-SHORTTERM-CORRA-OIS", CAD_CORRA, ACT_365F, TERM, 1, 1, FOLLOWING);

  public static final FixedOvernightSwapConvention CHF_FIXED_TERM_SARON_OIS =
      makeConvention(
          "CHF-FIXED-TERM-SARON-OIS", CHF_SARON, ACT_360, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CHF_FIXED_1Y_SARON_OIS =
      makeConvention("CHF-FIXED-1Y-SARON-OIS", CHF_SARON, ACT_360, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CHF_FIXED_SHORTTERM_SARON_OIS =
      makeConvention("CHF-FIXED-SHORTTERM-SARON-OIS", CHF_SARON, ACT_360, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention CZK_FIXED_TERM_CZEONIA_OIS =
      makeConvention(
          "CZK-FIXED-TERM-CZEONIA-OIS", CZK_CZEONIA, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CZK_FIXED_1Y_CZEONIA_OIS =
      makeConvention(
          "CZK-FIXED-1Y-CZEONIA-OIS", CZK_CZEONIA, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CZK_FIXED_SHORTTERM_CZEONIA_OIS =
      makeConvention(
          "CZK-FIXED-SHORTTERM-CZEONIA-OIS", CZK_CZEONIA, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_TERM_EONIA_OIS =
      makeConvention(
          "EUR-FIXED-TERM-EONIA-OIS", EUR_EONIA, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_1Y_EONIA_OIS =
      makeConvention("EUR-FIXED-1Y-EONIA-OIS", EUR_EONIA, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_SHORTTERM_EONIA_OIS =
      makeConvention("EUR-FIXED-SHORTTERM-EONIA-OIS", EUR_EONIA, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_TERM_ESTR_OIS =
      makeConvention("EUR-FIXED-TERM-ESTR-OIS", EUR_ESTR, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_1Y_ESTR_OIS =
      makeConvention("EUR-FIXED-1Y-ESTR-OIS", EUR_ESTR, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention EUR_FIXED_SHORTTERM_ESTR_OIS =
      makeConvention("EUR-FIXED-SHORTTERM-ESTR-OIS", EUR_ESTR, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention GBP_FIXED_TERM_SONIA_OIS =
      makeConvention(
          "GBP-FIXED-TERM-SONIA-OIS", GBP_SONIA, ACT_365F, TERM, 0, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention GBP_FIXED_1Y_SONIA_OIS =
      makeConvention("GBP-FIXED-1Y-SONIA-OIS", GBP_SONIA, ACT_365F, P12M, 0, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention GBP_FIXED_SHORTTERM_SONIA_OIS =
      makeConvention("GBP-FIXED-SHORTTERM-SONIA-OIS", GBP_SONIA, ACT_365F, TERM, 0, 0, FOLLOWING);

  public static final FixedOvernightSwapConvention HKD_FIXED_TERM_HONIA_OIS =
      makeConvention(
          "HKD-FIXED-TERM-HONIA-OIS", HKD_HONIA, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention HKD_FIXED_3M_HONIA_OIS =
      makeConvention("HKD-FIXED-3M-HONIA-OIS", HKD_HONIA, ACT_365F, P3M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention HKD_FIXED_SHORTTERM_HONIA_OIS =
      makeConvention("HKD-FIXED-SHORTTERM-HONIA-OIS", HKD_HONIA, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention IDR_FIXED_TERM_INDONIA_OIS =
      makeConvention(
          "IDR-FIXED-TERM-INDONIA-OIS", IDR_INDONIA, ACT_360, TERM, 1, 1, MODIFIED_FOLLOWING);
  public static final FixedOvernightSwapConvention IDR_FIXED_SHORTTERM_INDONIA_OIS =
      makeConvention(
          "IDR-FIXED-SHORTTERM-INDONIA-OIS", IDR_INDONIA, ACT_360, TERM, 1, 1, FOLLOWING);
  public static final FixedOvernightSwapConvention IDR_FIXED_1Y_INDONIA_OIS =
      makeConvention(
          "IDR-FIXED-1Y-INDONIA-OIS", IDR_INDONIA, ACT_360, P12M, 1, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention JPY_FIXED_TERM_TONAR_OIS =
      makeConvention(
          "JPY-FIXED-TERM-TONAR-OIS", JPY_TONAR, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention JPY_FIXED_1Y_TONAR_OIS =
      makeConvention("JPY-FIXED-1Y-TONAR-OIS", JPY_TONAR, ACT_365F, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention JPY_FIXED_SHORTTERM_TONAR_OIS =
      makeConvention("JPY-FIXED-SHORTTERM-TONAR-OIS", JPY_TONAR, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_TERM_MYOR_OIS =
      makeConvention("MYR-FIXED-TERM-MYOR-OIS", MYR_MYOR, ACT_365F, TERM, 2, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_SHORTTERM_MYOR_OIS =
      makeConvention("MYR-FIXED-SHORTTERM-MYOR-OIS", MYR_MYOR, ACT_365F, TERM, 2, 0, FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_3M_MYOR_OIS =
      makeConvention("MYR-FIXED-3M-MYOR-OIS", MYR_MYOR, ACT_365F, P3M, 2, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_TERM_MYOR_OIS_OFFSHORE =
      makeConvention(
          "MYR-FIXED-TERM-MYOR-OIS-OFFSHORE",
          MYR_MYOR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          0,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_SHORTTERM_MYOR_OIS_OFFSHORE =
      makeConvention(
          "MYR-FIXED-SHORTTERM-MYOR-OIS-OFFSHORE",
          MYR_MYOR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          0,
          FOLLOWING);

  public static final FixedOvernightSwapConvention MYR_FIXED_3M_MYOR_OIS_OFFSHORE =
      makeConvention(
          "MYR-FIXED-3M-MYOR-OIS-OFFSHORE",
          MYR_MYOR_OFFSHORE,
          ACT_365F,
          P3M,
          2,
          0,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention NZD_FIXED_TERM_NZIONA_OIS =
      makeConvention(
          "NZD-FIXED-TERM-NZIONA-OIS", NZD_NZIONA, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention NZD_FIXED_1Y_NZIONA_OIS =
      makeConvention(
          "NZD-FIXED-1Y-NZIONA-OIS", NZD_NZIONA, ACT_365F, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention NZD_FIXED_SHORTTERM_NZIONA_OIS =
      makeConvention("NZD-FIXED-SHORTTERM-NZIONA-OIS", NZD_NZIONA, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_TERM_WIRON_OIS =
      makeConvention(
          "PLN-FIXED-TERM-WIRON-OIS", PLN_WIRON, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_SHORTTERM_WIRON_OIS =
      makeConvention("PLN-FIXED-SHORTTERM-WIRON-OIS", PLN_WIRON, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_1Y_WIRON_OIS =
      makeConvention("PLN-FIXED-1Y-WIRON-OIS", PLN_WIRON, ACT_365F, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention SGD_FIXED_TERM_SORA_OIS =
      makeConvention("SGD-FIXED-TERM-SORA-OIS", SGD_SORA, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention SGD_FIXED_6M_SORA_OIS =
      makeConvention("SGD-FIXED-6M-SORA-OIS", SGD_SORA, ACT_365F, P6M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention SGD_FIXED_SHORTTERM_SORA_OIS =
      makeConvention("SGD-FIXED-SHORTTERM-SORA-OIS", SGD_SORA, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_TERM_THOR_OIS =
      makeConvention("THB-FIXED-TERM-THOR-OIS", THB_THOR, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_3M_THOR_OIS =
      makeConvention("THB-FIXED-3M-THOR-OIS", THB_THOR, ACT_365F, P3M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_3M_THOR_OIS_OFFSHORE =
      makeConvention(
          "THB-FIXED-3M-THOR-OIS-OFFSHORE",
          THB_THOR_OFFSHORE,
          ACT_365F,
          P3M,
          2,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_SHORTTERM_THOR_OIS =
      makeConvention("THB-FIXED-SHORTTERM-THOR-OIS", THB_THOR, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_SHORTTERM_THOR_OIS_OFFSHORE =
      makeConvention(
          "THB-FIXED-SHORTTERM-THOR-OIS-OFFSHORE",
          THB_THOR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          2,
          FOLLOWING);

  public static final FixedOvernightSwapConvention THB_FIXED_TERM_THOR_OIS_OFFSHORE =
      makeConvention(
          "THB-FIXED-TERM-THOR-OIS-OFFSHORE",
          THB_THOR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention INR_FIXED_TERM_OMIBOR_OIS =
      makeConvention(
          "INR-FIXED-TERM-OMIBOR-OIS", INR_OMIBOR, ACT_365F, TERM, 0, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention INR_FIXED_6M_OMIBOR_OIS =
      makeConvention(
          "INR-FIXED-6M-OMIBOR-OIS", INR_OMIBOR, ACT_365F, P6M, 0, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention INR_FIXED_6M_OMIBOR_OIS_OFFSHORE =
      makeConvention(
          "INR-FIXED-6M-OMIBOR-OIS-OFFSHORE",
          INR_OMIBOR_OFFSHORE,
          ACT_365F,
          P6M,
          0,
          1,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention INR_FIXED_SHORTTERM_OMIBOR_OIS =
      makeConvention("INR-FIXED-SHORTTERM-OMIBOR-OIS", INR_OMIBOR, ACT_365F, TERM, 0, 1, FOLLOWING);

  public static final FixedOvernightSwapConvention INR_FIXED_TERM_OMIBOR_OIS_OFFSHORE =
      makeConvention(
          "INR-FIXED-TERM-OMIBOR-OIS-OFFSHORE",
          INR_OMIBOR_OFFSHORE,
          ACT_365F,
          TERM,
          0,
          1,
          MODIFIED_FOLLOWING);
  public static final FixedOvernightSwapConvention INR_FIXED_SHORTTERM_OMIBOR_OIS_OFFSHORE =
      makeConvention(
          "INR-FIXED-SHORTTERM-OMIBOR-OIS-OFFSHORE",
          INR_OMIBOR_OFFSHORE,
          ACT_365F,
          TERM,
          0,
          1,
          FOLLOWING);

  public static final FixedOvernightSwapConvention TRY_FIXED_TERM_TLREF_OIS =
      makeConvention(
          "TRY-FIXED-TERM-TLREF-OIS", TRY_TLREF, ACT_365F, TERM, 1, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention TRY_FIXED_3M_TLREF_OIS =
      makeConvention("TRY-FIXED-3M-TLREF-OIS", TRY_TLREF, ACT_365F, P3M, 1, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention TRY_FIXED_SHORTTERM_TLREF_OIS =
      makeConvention("TRY-FIXED-SHORTTERM-TLREF-OIS", TRY_TLREF, ACT_365F, TERM, 1, 1, FOLLOWING);

  public static final FixedOvernightSwapConvention BRL_FIXED_TERM_CDI_OIS =
      makeBrlConvention(0, "BRL-FIXED-TERM-CDI-OIS", OvernightIndices.BRL_CDI);

  public static final FixedOvernightSwapConvention USD_FIXED_TERM_SOFR_OIS =
      makeConvention("USD-FIXED-TERM-SOFR-OIS", USD_SOFR, ACT_360, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention USD_FIXED_1Y_SOFR_OIS =
      makeConvention("USD-FIXED-1Y-SOFR-OIS", USD_SOFR, ACT_360, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention USD_FIXED_SHORTTERM_SOFR_OIS =
      makeConvention("USD-FIXED-SHORTTERM-SOFR-OIS", USD_SOFR, ACT_360, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention USD_FIXED_TERM_FED_FUND_OIS =
      makeConvention(
          "USD-FIXED-TERM-FED-FUND-OIS", USD_FED_FUND, ACT_360, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention USD_FIXED_1Y_FED_FUND_OIS =
      makeConvention(
          "USD-FIXED-1Y-FED-FUND-OIS", USD_FED_FUND, ACT_360, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention USD_FIXED_SHORTTERM_FED_FUND_OIS =
      makeConvention(
          "USD-FIXED-SHORTTERM-FED-FUND-OIS", USD_FED_FUND, ACT_360, TERM, 2, 2, FOLLOWING);

  // COP
  public static final FixedOvernightSwapConvention COP_FIXED_TERM_OIBR_OIS =
      makeConvention("COP-FIXED-TERM-OIBR-OIS", COP_OIBR, ACT_360, TERM, 0, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention COP_FIXED_SHORTTERM_OIBR_OIS =
      makeConvention("COP-FIXED-SHORTTERM-OIBR-OIS", COP_OIBR, ACT_360, TERM, 0, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention COP_FIXED_3M_OIBR_OIS =
      makeConvention("COP-FIXED-3M-OIBR-OIS", COP_OIBR, ACT_360, P3M, 0, 2, MODIFIED_FOLLOWING);

  // CLP
  public static final FixedOvernightSwapConvention CLP_FIXED_TERM_TNA_OIS =
      makeConvention("CLP-FIXED-TERM-TNA-OIS", CLP_TNA, ACT_360, TERM, 0, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CLP_FIXED_SHORTTERM_TNA_OIS =
      makeConvention("CLP-FIXED-SHORTTERM-TNA-OIS", CLP_TNA, ACT_360, TERM, 0, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention CLP_FIXED_6M_TNA_OIS =
      makeConvention("CLP-FIXED-6M-TNA-OIS", CLP_TNA, ACT_360, P6M, 0, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention NOK_FIXED_1Y_NOWA_OIS =
      makeConventionForDualPaymentLag(
          "NOK-FIXED-1Y-NOWA-OIS", NOK_NOWA, ACT_365F, P12M, 2, 1, 2, MODIFIED_FOLLOWING, null);

  public static final FixedOvernightSwapConvention NOK_FIXED_TERM_NOWA_OIS =
      makeConventionForDualPaymentLag(
          "NOK-FIXED-TERM-NOWA-OIS", NOK_NOWA, ACT_365F, TERM, 2, 1, 2, MODIFIED_FOLLOWING, null);

  public static final FixedOvernightSwapConvention NOK_FIXED_SHORTTERM_NOWA_OIS =
      makeConventionForDualPaymentLag(
          "NOK-FIXED-SHORTTERM-NOWA-OIS", NOK_NOWA, ACT_365F, TERM, 2, 1, 2, FOLLOWING, null);

  public static final FixedOvernightSwapConvention SEK_FIXED_1Y_SWESTR_OIS =
      makeConvention(
          "SEK-FIXED-1Y-SWESTR-OIS", SEK_SWESTR, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention SEK_FIXED_TERM_SWESTR_OIS =
      makeConvention(
          "SEK-FIXED-TERM-SWESTR-OIS", SEK_SWESTR, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention SEK_FIXED_SHORTTERM_SWESTR_OIS =
      makeConvention("SEK-FIXED-SHORTTERM-SWESTR-OIS", SEK_SWESTR, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention DKK_FIXED_1Y_DESTR_OIS =
      makeConvention("DKK-FIXED-1Y-DESTR-OIS", DKK_DESTR, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention DKK_FIXED_TERM_DESTR_OIS =
      makeConvention(
          "DKK-FIXED-TERM-DESTR-OIS", DKK_DESTR, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention DKK_FIXED_SHORTTERM_DESTR_OIS =
      makeConvention("DKK-FIXED-SHORTTERM-DESTR-OIS", DKK_DESTR, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention HUF_FIXED_1Y_BUBORON_OIS =
      makeConvention(
          "HUF-FIXED-1Y-BUBORON-OIS", HUF_BUBORON, ACT_360, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention HUF_FIXED_TERM_BUBORON_OIS =
      makeConvention(
          "HUF-FIXED-TERM-BUBORON-OIS", HUF_BUBORON, ACT_360, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention HUF_FIXED_SHORTTERM_BUBORON_OIS =
      makeConvention(
          "HUF-FIXED-SHORTTERM-BUBORON-OIS", HUF_BUBORON, ACT_360, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_1Y_POLONIA_OIS =
      makeConvention(
          "PLN-FIXED-1Y-POLONIA-OIS", PLN_POLONIA, ACT_365F, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_TERM_POLONIA_OIS =
      makeConvention(
          "PLN-FIXED-TERM-POLONIA-OIS", PLN_POLONIA, ACT_365F, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention PLN_FIXED_SHORTTERM_POLONIA_OIS =
      makeConvention(
          "PLN-FIXED-SHORTTERM-POLONIA-OIS", PLN_POLONIA, ACT_365F, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention RUB_FIXED_1Y_RUONIA_OIS =
      makeConvention(
          "RUB-FIXED-1Y-RUONIA-OIS", RUB_RUONIA, ACT_ACT_ISDA, P12M, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention RUB_FIXED_TERM_RUONIA_OIS =
      makeConvention(
          "RUB-FIXED-TERM-RUONIA-OIS", RUB_RUONIA, ACT_ACT_ISDA, TERM, 1, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention RUB_FIXED_SHORTTERM_RUONIA_OIS =
      makeConvention(
          "RUB-FIXED-SHORTTERM-RUONIA-OIS", RUB_RUONIA, ACT_ACT_ISDA, TERM, 1, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention BRL_FIXED_TERM_CDI_OIS_OFFSHORE =
      makeBrlConvention(1, "BRL-FIXED-TERM-CDI-OIS-OFFSHORE", OffshoreIndices.BRL_CDI_OFFSHORE);

  public static final FixedOvernightSwapConvention COP_FIXED_TERM_OIBR_OIS_OFFSHORE =
      makeConvention(
          "COP-FIXED-TERM-OIBR-OIS-OFFSHORE",
          COP_OIBR_OFFSHORE,
          ACT_360,
          TERM,
          0,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention COP_FIXED_SHORTTERM_OIBR_OIS_OFFSHORE =
      makeConvention(
          "COP-FIXED-SHORTTERM-OIBR-OIS-OFFSHORE",
          COP_OIBR_OFFSHORE,
          ACT_360,
          TERM,
          0,
          2,
          FOLLOWING);

  public static final FixedOvernightSwapConvention COP_FIXED_3M_OIBR_OIS_OFFSHORE =
      makeConvention(
          "COP-FIXED-3M-OIBR-OIS-OFFSHORE",
          COP_OIBR_OFFSHORE,
          ACT_360,
          P3M,
          0,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CLP_FIXED_TERM_TNA_OIS_OFFSHORE =
      makeConvention(
          "CLP-FIXED-TERM-TNA-OIS-OFFSHORE",
          CLP_TNA_OFFSHORE,
          ACT_360,
          TERM,
          0,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention CLP_FIXED_SHORTTERM_TNA_OIS_OFFSHORE =
      makeConvention(
          "CLP-FIXED-SHORTTERM-TNA-OIS-OFFSHORE", CLP_TNA_OFFSHORE, ACT_360, TERM, 0, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention CLP_FIXED_6M_TNA_OIS_OFFSHORE =
      makeConvention(
          "CLP-FIXED-6M-TNA-OIS-OFFSHORE",
          CLP_TNA_OFFSHORE,
          ACT_360,
          P6M,
          0,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ZAR_FIXED_TERM_ZARONIA_OIS =
      makeConvention(
          "ZAR-FIXED-TERM-ZARONIA-OIS", ZAR_ZARONIA, ACT_365F, TERM, 2, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ZAR_FIXED_SHORTTERM_ZARONIA_OIS =
      makeConvention(
          "ZAR-FIXED-SHORTTERM-ZARONIA-OIS", ZAR_ZARONIA, ACT_365F, P12M, 2, 0, FOLLOWING);

  public static final FixedOvernightSwapConvention ZAR_FIXED_1Y_ZARONIA_OIS =
      makeConvention(
          "ZAR-FIXED-1Y-ZARONIA-OIS", ZAR_ZARONIA, ACT_365F, P12M, 2, 0, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_TERM_SHIR_OIS =
      makeConvention("ILS-FIXED-TERM-SHIR-OIS", ILS_SHIR, ACT_365F, TERM, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_SHORTTERM_SHIR_OIS =
      makeConvention("ILS-FIXED-SHORTTERM-SHIR-OIS", ILS_SHIR, ACT_365F, TERM, 2, 2, FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_1Y_SHIR_OIS =
      makeConvention("ILS-FIXED-1Y-SHIR-OIS", ILS_SHIR, ACT_365F, P12M, 2, 2, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_TERM_SHIR_OIS_OFFSHORE =
      makeConvention(
          "ILS-FIXED-TERM-SHIR-OIS-OFFSHORE",
          ILS_SHIR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_SHORTTERM_SHIR_OIS_OFFSHORE =
      makeConvention(
          "ILS-FIXED-SHORTTERM-SHIR-OIS-OFFSHORE",
          ILS_SHIR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          2,
          FOLLOWING);

  public static final FixedOvernightSwapConvention ILS_FIXED_1Y_SHIR_OIS_OFFSHORE =
      makeConvention(
          "ILS-FIXED-1Y-SHIR-OIS-OFFSHORE",
          ILS_SHIR_OFFSHORE,
          ACT_365F,
          P12M,
          2,
          2,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_TERM_KOFR_OIS =
      makeConvention("KRW-FIXED-TERM-KOFR-OIS", KRW_KOFR, ACT_365F, TERM, 2, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_SHORTTERM_KOFR_OIS =
      makeConvention("KRW-FIXED-SHORTTERM-KOFR-OIS", KRW_KOFR, ACT_365F, TERM, 2, 1, FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_3M_KOFR_OIS =
      makeConvention("KRW-FIXED-3M-KOFR-OIS", KRW_KOFR, ACT_365F, P3M, 2, 1, MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_TERM_KOFR_OIS_OFFSHORE =
      makeConvention(
          "KRW-FIXED-TERM-KOFR-OIS-OFFSHORE",
          KRW_KOFR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          1,
          MODIFIED_FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_SHORTTERM_KOFR_OIS_OFFSHORE =
      makeConvention(
          "KRW-FIXED-SHORTTERM-KOFR-OIS-OFFSHORE",
          KRW_KOFR_OFFSHORE,
          ACT_365F,
          TERM,
          2,
          1,
          FOLLOWING);

  public static final FixedOvernightSwapConvention KRW_FIXED_3M_KOFR_OIS_OFFSHORE =
      makeConvention(
          "KRW-FIXED-3M-KOFR-OIS-OFFSHORE",
          KRW_KOFR_OFFSHORE,
          ACT_365F,
          P3M,
          2,
          1,
          MODIFIED_FOLLOWING);

  public static FixedOvernightSwapConvention makeConvention(
      String name,
      OvernightIndex index,
      DayCount dayCount,
      Frequency frequency,
      int paymentLag,
      int spotLag,
      BusinessDayConvention businessDayConvention) {

    return makeOffshoreConvention(
        name, index, dayCount, frequency, paymentLag, spotLag, businessDayConvention, null);
  }

  /**
   * @param calendarId to override default fixing calendar from index for offshore
   */
  private static FixedOvernightSwapConvention makeOffshoreConvention(
      String name,
      OvernightIndex index,
      DayCount dayCount,
      Frequency frequency,
      int paymentLag,
      int spotLag,
      BusinessDayConvention businessDayConvention,
      HolidayCalendarId calendarId) {

    return makeConventionForDualPaymentLag(
        name,
        index,
        dayCount,
        frequency,
        paymentLag,
        paymentLag,
        spotLag,
        businessDayConvention,
        calendarId);
  }

  /**
   * @param calendarId defaults to index fixing calendar if null
   */
  private static FixedOvernightSwapConvention makeConventionForDualPaymentLag(
      String name,
      OvernightIndex index,
      DayCount dayCount,
      Frequency frequency,
      int fixedPaymentLag,
      int floatingPaymentLag,
      int spotLag,
      BusinessDayConvention businessDayConvention,
      @Nullable HolidayCalendarId calendarId) {

    if (calendarId == null) {
      calendarId = index.getFixingCalendar();
    }

    var fixedPaymentDateOffset = DaysAdjustment.ofBusinessDays(fixedPaymentLag, calendarId);
    var floatingPaymentDateOffset = DaysAdjustment.ofBusinessDays(floatingPaymentLag, calendarId);
    var spotDateOffset = DaysAdjustment.ofBusinessDays(spotLag, calendarId);

    var fixedLegConvention =
        fixedLegConvention(
            index,
            frequency,
            dayCount,
            DEFAULT,
            fixedPaymentDateOffset,
            businessDayConvention,
            calendarId);
    var overnightLegConvention =
        overnightLegConvention(
            index,
            frequency,
            COMPOUNDED,
            floatingPaymentDateOffset,
            businessDayConvention,
            1,
            calendarId);

    return ImmutableFixedOvernightSwapConvention.of(
        name, fixedLegConvention, overnightLegConvention, spotDateOffset);
  }

  private static FixedOvernightSwapConvention makeBrlConvention(
      int payDateOffset, String conventionName, OvernightIndex index) {
    var calendar = index.getFixingCalendar();
    var paymentDateOffset = DaysAdjustment.ofBusinessDays(payDateOffset, calendar);
    var spotDateOffset = DaysAdjustment.ofBusinessDays(0, calendar);

    var fixedLegConvention =
        fixedLegConvention(
            index,
            Frequency.TERM,
            DayCount.ofBus252(BRBD),
            FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE,
            paymentDateOffset,
            FOLLOWING,
            calendar);
    var overnightLegConvention =
        overnightLegConvention(
            index,
            Frequency.TERM,
            OVERNIGHT_COMPOUNDED_ANNUAL_RATE,
            paymentDateOffset,
            FOLLOWING,
            0,
            calendar);

    return ImmutableFixedOvernightSwapConvention.of(
        conventionName, fixedLegConvention, overnightLegConvention, spotDateOffset);
  }

  private static OvernightRateSwapLegConvention overnightLegConvention(
      OvernightIndex index,
      Frequency frequency,
      OvernightAccrualMethod overnightAccrualMethod,
      DaysAdjustment paymentDateOffset,
      BusinessDayConvention businessDayConvention,
      int rateCutoffDays,
      HolidayCalendarId calendarId) {
    return OvernightRateSwapLegConvention.builder()
        .index(index)
        .accrualMethod(overnightAccrualMethod)
        .accrualFrequency(frequency)
        .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(businessDayConvention, calendarId))
        .paymentFrequency(frequency)
        .paymentDateOffset(paymentDateOffset)
        .stubConvention(StubConvention.SHORT_INITIAL)
        .rateCutOffDays(rateCutoffDays)
        .build();
  }

  private static FixedRateSwapLegConvention fixedLegConvention(
      OvernightIndex index,
      Frequency frequency,
      DayCount dayCount,
      FixedAccrualMethod fixedAccrualMethod,
      DaysAdjustment paymentDateOffset,
      BusinessDayConvention businessDayConvention,
      HolidayCalendarId calendarId) {
    return FixedRateSwapLegConvention.builder()
        .currency(index.getCurrency())
        .dayCount(dayCount)
        .accrualMethod(fixedAccrualMethod)
        .accrualFrequency(frequency)
        .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(businessDayConvention, calendarId))
        .paymentFrequency(frequency)
        .paymentDateOffset(paymentDateOffset)
        .stubConvention(StubConvention.SHORT_INITIAL)
        .build();
  }
}
