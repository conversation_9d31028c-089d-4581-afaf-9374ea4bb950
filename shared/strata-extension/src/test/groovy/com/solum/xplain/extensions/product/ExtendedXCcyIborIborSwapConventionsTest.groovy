package com.solum.xplain.extensions.product

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.basics.date.HolidayCalendarIds.DKCO
import static com.opengamma.strata.basics.date.HolidayCalendarIds.GBLO
import static com.opengamma.strata.basics.date.HolidayCalendarIds.MXMC
import static com.opengamma.strata.basics.date.HolidayCalendarIds.NZAU
import static com.opengamma.strata.basics.date.HolidayCalendarIds.PLWA
import static com.opengamma.strata.basics.date.HolidayCalendarIds.SEST
import static com.opengamma.strata.basics.date.HolidayCalendarIds.USNY
import static com.opengamma.strata.basics.date.HolidayCalendarIds.ZAJO
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.ILTA
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.KRSE
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.SGSI

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.common.PayReceive
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention
import java.time.LocalDate
import spock.lang.Specification

class ExtendedXCcyIborIborSwapConventionsTest extends Specification {

  def "should return correctly constructed DKK_CIBOR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.DKK_CIBOR_3M_USD_LIBOR_3M

    then:
    convention.name == "DKK-CIBOR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.DKK
    convention.getSpreadLeg().getIndex() == IborIndices.DKK_CIBOR_3M
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_360
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, DKCO.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, DKCO.combinedWith(GBLO_USNY))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, DKCO.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed ILS_TLBOR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.ILS_TLBOR_3M_USD_LIBOR_3M

    then:
    convention.name == "ILS-TLBOR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.ILS
    convention.getSpreadLeg().getIndex() == IborIndex.of("ILS-TLBOR-3M")
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365F
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, ILTA.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(ILTA.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, ILTA.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed PLN_WIBOR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.PLN_WIBOR_3M_USD_LIBOR_3M

    then:
    convention.name == "PLN-WIBOR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.PLN
    convention.getSpreadLeg().getIndex() == IborIndices.PLN_WIBOR_3M
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365F
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, PLWA.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(PLWA.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, PLWA.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed SEK_STIBOR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.SEK_STIBOR_3M_USD_LIBOR_3M

    then:
    convention.name == "SEK-STIBOR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.SEK
    convention.getSpreadLeg().getIndex() == IborIndices.SEK_STIBOR_3M
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_360
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SEST.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(SEST.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, SEST.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed SGD_SOR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.SGD_SOR_3M_USD_LIBOR_3M

    then:
    convention.name == "SGD-SOR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.SGD
    convention.getSpreadLeg().getIndex() == IborIndex.of("SGD-SOR-3M")
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365F
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(SGSI.combinedWith(GBLO_USNY)))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(SGSI.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, SGSI.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed ZAR_JIBAR_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.ZAR_JIBAR_3M_USD_LIBOR_3M

    then:
    convention.name == "ZAR-JIBAR-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.ZAR
    convention.getSpreadLeg().getIndex() == IborIndices.ZAR_JIBAR_3M
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365F
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, ZAJO.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(ZAJO.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, ZAJO.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed NZD_BKBM_3M_USD_LIBOR_3M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.NZD_BKBM_3M_USD_LIBOR_3M

    then:
    convention.name == "NZD-BKBM-3M-USD-LIBOR-3M"
    convention.getSpreadLeg().getCurrency() == Currency.NZD
    convention.getSpreadLeg().getIndex() == IborIndices.NZD_BKBM_3M
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365F
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, NZAU.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_3M
    convention.getFlatLeg().accrualFrequency == Frequency.P3M
    convention.getFlatLeg().paymentFrequency == Frequency.P3M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(NZAU.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, NZAU.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed MXN-TIIE-4W-USD-LIBOR-1M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.MXN_TIIE_4W_USD_LIBOR_1M

    then:
    convention.name == "MXN-TIIE-4W-USD-LIBOR-1M"
    convention.getSpreadLeg().getCurrency() == Currency.MXN
    convention.getSpreadLeg().getIndex() == IborIndices.MXN_TIIE_4W
    convention.getSpreadLeg().accrualFrequency == Frequency.P4W
    convention.getSpreadLeg().paymentFrequency == Frequency.P4W
    convention.getSpreadLeg().dayCount == DayCounts.ACT_360
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, MXMC.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_1M
    convention.getFlatLeg().accrualFrequency == Frequency.P1M
    convention.getFlatLeg().paymentFrequency == Frequency.P1M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(MXMC.combinedWith(GBLO_USNY)))

    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, MXMC.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correctly constructed KRW-CD-13W-USD-LIBOR-6M convention"() {
    when:
    def convention = ExtendedXCcyIborIborSwapConventions.KRW_CD_13W_USD_LIBOR_6M

    then:
    convention.name == "KRW-CD-13W-USD-LIBOR-6M"
    convention.getSpreadLeg().getCurrency() == Currency.KRW
    convention.getSpreadLeg().getIndex() == IborIndex.of("KRW-CD-13W")
    convention.getSpreadLeg().accrualFrequency == Frequency.P3M
    convention.getSpreadLeg().paymentFrequency == Frequency.P3M
    convention.getSpreadLeg().dayCount == DayCounts.ACT_365_ACTUAL
    convention.getSpreadLeg().notionalExchange
    convention.getSpreadLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, KRSE.combinedWith(GBLO_USNY))

    convention.getFlatLeg().getCurrency() == Currency.USD
    convention.getFlatLeg().getIndex() == IborIndices.USD_LIBOR_6M
    convention.getFlatLeg().accrualFrequency == Frequency.P6M
    convention.getFlatLeg().paymentFrequency == Frequency.P6M
    convention.getFlatLeg().dayCount == DayCounts.ACT_360
    convention.getFlatLeg().notionalExchange
    convention.getFlatLeg().accrualBusinessDayAdjustment == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO.combinedWith(KRSE.combinedWith(GBLO_USNY)))
    convention.getSpotDateOffset() == DaysAdjustment.ofBusinessDays(2, KRSE.combinedWith(GBLO.combinedWith(USNY)))
  }

  def "should return correct leg stub convention for #conventionName"() {
    setup:
    def convention = XCcyIborIborSwapConvention.of(conventionName)
    def startDate = LocalDate.of(2022, 01, 01)
    def enDate = LocalDate.of(2032, 01, 01)
    expect:

    def leg = convention.getFlatLeg().toLeg(startDate, enDate, PayReceive.PAY, 0d)
    def stubConvention = leg.getAccrualSchedule().getStubConvention().orElseThrow()
    stubConvention == StubConvention.SMART_INITIAL

    and:
    def spreadLeg = convention.getSpreadLeg().toLeg(startDate, enDate, PayReceive.PAY, 0d)
    def spreadStubConvention = spreadLeg.getAccrualSchedule().getStubConvention().orElseThrow()
    spreadStubConvention == StubConvention.SMART_INITIAL

    where:
    conventionName << XCcyIborIborSwapConvention.extendedEnum().lookupAll().keySet()
  }
}
